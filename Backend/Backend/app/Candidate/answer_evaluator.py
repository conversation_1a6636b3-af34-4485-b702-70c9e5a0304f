import google.generativeai as genai
from config import GEMINI_API_KEY
from schemas import EvaluationResult, AnswerRequest
from typing import List

genai.configure(api_key=GEMINI_API_KEY)
model = genai.GenerativeModel("models/gemini-1.5-flash")

async def evaluate_answer(question: str, answer: str) -> EvaluationResult:
    prompt = f"""
    Evaluate the following candidate answer to the given question.

    Question:
    {question}

    Candidate's Answer:
    {answer}

    Provide a score out of 10 and a short explanation of strengths and weaknesses.
    Format:
    Score: <number>
    Explanation: <text>
    """

    response = model.generate_content(prompt)
    output = response.text.strip()

    try:
        score_line = next(line for line in output.split("\n") if "Score" in line)
        explanation_line = next(line for line in output.split("\n") if "Explanation" in line)

        score = float(score_line.split(":")[1].strip())
        explanation = explanation_line.split(":", 1)[1].strip()

        return EvaluationResult(score=round(score, 2), explanation=explanation)
    except Exception as e:
        raise ValueError(f"Failed to parse Gemini response: {output}")

# 🔁 Bulk evaluation
async def evaluate_multiple_answers(answers: List[AnswerRequest]) -> List[EvaluationResult]:
    results = []
    for item in answers:
        result = await evaluate_answer(item.question, item.answer)
        results.append(result)
    return results
