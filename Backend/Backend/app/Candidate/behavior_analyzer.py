import google.generativeai as genai
from config import GEMINI_API_KEY

genai.configure(api_key=GEMINI_API_KEY)
model = genai.GenerativeModel("models/gemini-1.5-flash")

async def analyze_behavior(video_url: str):
    # In real life, use a vision/video model. Here we simulate via LLM.
    prompt = f"""
    A candidate was interviewed via webcam. The video is hosted at:
    {video_url}

    Based on the assumption the video contains face and voice,
    simulate an analysis of these metrics:
    - Eye Contact
    - Speaking Pace
    - Background Distraction
    - Audio Quality

    Give concise and realistic responses for each metric.
    """

    response = model.generate_content(prompt)
    return {"analysis": response.text.strip()}
