import fitz  # PyMuPDF
from fastapi import UploadFile
from typing import Union

async def extract_text_from_file(file: UploadFile) -> str:
    ext = file.filename.lower()
    content = await file.read()

    if ext.endswith(".pdf"):
        return extract_text_from_pdf_bytes(content)
    elif ext.endswith(".txt"):
        return content.decode("utf-8")
    else:
        raise ValueError("Unsupported file type. Upload PDF or TXT.")

def extract_text_from_pdf_bytes(data: Union[bytes, bytearray]) -> str:
    text = ""
    with fitz.open(stream=data, filetype="pdf") as doc:
        for page in doc:
            text += page.get_text()
    return text
