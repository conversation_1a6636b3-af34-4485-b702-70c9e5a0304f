import google.generativeai as genai
from app.core.config import GOOGLE_API_KEY

genai.configure(api_key=GOOGLE_API_KEY)
model = genai.GenerativeModel('gemini-2.0-flash')

async def generate_questions_from_resume_and_jd(resume_text: str, jd_text: str):
    prompt = f"""
    You are an interview question generator. Based on the candidate's resume and the job description below,
    generate 5 specific, relevant interview questions.

    Resume:
    {resume_text}

    Job Description:
    {jd_text}

    Format the questions as a numbered list, each question on a new line.
    """

    response = model.generate_content(prompt)
    raw_output = response.text.strip()
    questions = [q.strip().lstrip("1234567890. ").strip() for q in raw_output.split("\n") if q.strip()]
    return questions[:5]
