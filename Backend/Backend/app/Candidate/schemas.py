from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from enum import Enum

# --- Original schemas.py content ---

class AnswerRequest(BaseModel):
    question: str
    answer: str

class EvaluationResult(BaseModel):
    score: float
    explanation: str

class BulkEvaluationRequest(BaseModel):
    answers: List[AnswerRequest]

class BulkEvaluationResult(BaseModel):
    evaluations: List[EvaluationResult]

class TranscriptSaveRequest(BaseModel):
    candidate_name: str
    questions: List[str]
    answers: List[str]
    transcript_text: str

# --- Models from pre_interview_process_main.py ---

class ExperienceLevel(str, Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"

class LearningPlanRequest(BaseModel):
    experience_level: ExperienceLevel
    resume_text: Optional[str] = None
    job_description: Optional[str] = None