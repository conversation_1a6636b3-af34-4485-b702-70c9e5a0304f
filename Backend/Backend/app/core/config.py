import os
import logging
from pydantic_settings import BaseSettings
from pathlib import Path
from typing import Dict, Any, Optional
from dotenv import load_dotenv
import google.generativeai as genai
from urllib.parse import quote_plus

# Load environment variables from .env file
load_dotenv()

GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

if not GEMINI_API_KEY:
    raise EnvironmentError("Missing GEMINI_API_KEY in environment variables.")

# --- Logging Configuration ---
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log')
    ]
)

logger = logging.getLogger(__name__)

# --- File Upload Configuration ---
UPLOAD_FOLDER = "uploads"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
ALLOWED_EXTENSIONS = {'pdf', 'docx'}

# Initialize Gemini
GOOGLE_API_KEY = os.getenv("GEMINI_API_KEY")

if not GOOGLE_API_KEY:
    logger.error("GEMINI_API_KEY environment variable not set")
    raise ValueError("GEMINI_API_KEY environment variable not set")

try:
    genai.configure(api_key=GOOGLE_API_KEY)
    model = genai.GenerativeModel('gemini-2.0-flash')
    logger.info("Successfully initialized Gemini model")
except Exception as e:
    logger.error(f"Error initializing Gemini: {str(e)}")
    raise

# PostgreSQL Configuration
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:GenAILakes@localhost:5432/interview_db")

class Settings(BaseSettings):
    db_user: str = "postgres"
    db_password: str = "GenAILakes"
    db_host: str = "localhost"
    db_port: str = "5432"
    db_name: str = "interview_db"
    SECRET_KEY: str = "c5b1861f39050253accc6e7733af1a0a329b382ed16caa59eeb8302bd648b28c"
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    ASSEMBLYAI_API_KEY: str = os.getenv("ASSEMBLYAI_API_KEY", "")
    ELEVENLABS_API_KEY: str = os.getenv("ELEVENLABS_API_KEY", "")
    GOOGLE_APPLICATION_CREDENTIALS: str = os.getenv("GOOGLE_APPLICATION_CREDENTIALS", "")
    
    # AI settings
    GEMINI_API_KEY: str = os.getenv("GEMINI_API_KEY", "")
    GOOGLE_API_KEY: str = os.getenv("GOOGLE_API_KEY", "")  # Alternative name
    GOOGLE_SEARCH_ENGINE_ID: str = os.getenv("GOOGLE_SEARCH_ENGINE_ID", "")
    
    BASE_DIR: Path = Path(__file__).resolve().parent.parent.parent
    UPLOAD_DIR: Path = BASE_DIR / "uploads"
    MODEL_NAME: str = "gemini-1.5-flash"
    MODEL_TEMPERATURE: float = 0.2
    JD_STORAGE: Dict[str, Dict[str, Any]] = {}

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields to prevent validation errors

# --- Statistics Configuration ---
STATS_LIMIT_MOST_ASKED: Dict[str, Any] = {}
STATS_LIMIT_SKILLS_FREQ: Dict[str, Any] = {}

# --- Repeated Questions Configuration ---
# A question is "repeated" if it has been asked more than this many times.
REPEATED_QUESTION_THRESHOLD = 1
REPEATED_QUESTIONS_LIMIT = 10

# --- Relevance Scoring Weights ---
# The weight given to a question matching the candidate's skills.
RELEVANCE_SCORE_SKILL_MATCH = 2
# The weight given to a question matching the job description keywords.
RELEVANCE_SCORE_KEYWORD_MATCH = 1

# --- Seeder Configuration ---
SEED_DATA_FILE = "seed_data.json"

# Instantiate the settings so they can be imported easily
settings = Settings()