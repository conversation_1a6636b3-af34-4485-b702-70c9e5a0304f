# app/database/connection.py

import logging
from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
import psycopg2
from psycopg2.extras import RealDictCursor
import hashlib
import json
from contextlib import contextmanager

logger = logging.getLogger(__name__)

DATABASE_URL = f"postgresql://{settings.db_user}:{settings.db_password}@{settings.db_host}:{settings.db_port}/{settings.db_name}"

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Error in database operation: {e}")
        db.rollback()
    finally:
        db.close()

def get_db_connection():
    """Get a direct PostgreSQL connection."""
    try:
        conn = psycopg2.connect(
            host=settings.db_host,
            port=settings.db_port,
            database=settings.db_name,
            user=settings.db_user,
            password=settings.db_password
        )
        return conn
    except Exception as e:
        logger.error(f"Error connecting to database: {str(e)}")
        return None

def init_database():
    """Initialize database tables."""
    try:
        conn = get_db_connection()
        if not conn:
            return False

        cursor = conn.cursor()

        # Create interview_questions table if it doesn't exist
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS interview_questions (
                id SERIAL PRIMARY KEY,
                question TEXT NOT NULL,
                question_hash VARCHAR(64) UNIQUE,
                skills TEXT[],
                difficulty VARCHAR(20),
                category VARCHAR(50),
                sample_answer TEXT,
                hints TEXT[],
                asked_count INTEGER DEFAULT 0,
                company VARCHAR(100),
                job_description_keywords TEXT[],
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_asked TIMESTAMP
            )
        """)

        conn.commit()
        cursor.close()
        conn.close()
        logger.info("Database tables initialized successfully")
        return True

    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        return False

@contextmanager
def get_db_context():
    """Context manager for database connections."""
    conn = get_db_connection()
    try:
        yield conn
    finally:
        if conn:
            conn.close()