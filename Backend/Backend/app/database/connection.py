# app/database/connection.py

import logging
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
import psycopg2
from psycopg2.extras import RealDictCursor
import hashlib
import json
from contextlib import contextmanager
#from config import DATABASE_URL, logger, settings # <-- IMPORT setting

DATABASE_URL = f"postgresql://{settings.db_user}:{settings.db_password}@{settings.db_host}:{settings.db_port}/{settings.db_name}"

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        print(f"Error in database operation: {e}")
        db.rollback()
    finally:
        db.close()