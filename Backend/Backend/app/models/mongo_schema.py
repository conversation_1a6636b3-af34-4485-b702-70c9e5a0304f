from pymongo import MongoClient, ASCENDING
from datetime import datetime
from typing import Dict, List, Any, Optional
import os

# MongoDB Connection
client = MongoClient("mongodb://localhost:27017/")
db = client[os.getenv("mongodb_name", "")]

def create_parsed_resumes_collection():
    """Schema for storing parsed resume data"""
    parsed_resumes = {
        "_id": str,  # ObjectId
        "user_id": str,  # UUID
        "parsed_data": {
            "name": str,
            "email": str,
            "phone": str,
            "skills": List[str],
            "experience": [
                {
                    "company": str,
                    "role": str,
                    "duration": str
                }
            ],
            "education": [
                {
                    "institution": str,
                    "degree": str,
                    "year": str
                }
            ],
            "certifications": List[str]
        },
        "created_at": datetime
    }
    
    db.parsed_resumes.create_index([("user_id", ASCENDING)])
    return parsed_resumes

def create_ai_responses_collection():
    """Schema for logging AI Agent responses"""
    ai_responses = {
        "_id": str,  # ObjectId
        "user_id": str,  # UUID
        "action": str,
        "input_data": Dict[str, Any],  # JSON data
        "response": Dict[str, Any],  # JSON data
        "created_at": datetime
    }
    
    db.ai_responses.create_index([("user_id", ASCENDING)])
    db.ai_responses.create_index([("action", ASCENDING)])
    return ai_responses

def create_job_search_logs_collection():
    """Schema for logging AI Agent activity related to job searches"""
    job_search_logs = {
        "_id": str,  # ObjectId
        "user_id": str,  # UUID
        "job_search_query": {
            "skills": List[str],
            "experience": int,
            "location": str
        },
        "results": [
            {
                "job_title": str,
                "company_name": str,
                "source": str,
                "job_link": str
            }
        ],
        "created_at": datetime
    }
    
    db.job_search_logs.create_index([("user_id", ASCENDING)])
    return job_search_logs

def create_interview_questions_collection():
    """Schema for storing dynamically generated interview questions"""
    interview_questions = {
        "_id": str,  # ObjectId
        "user_id": str,  # UUID
        "job_id": str,  # UUID
        "questions": [
            {
                "question": str,
                "candidate_answer": str,
                "ai_answer": str,
                "score": float
            }
        ],
        "created_at": datetime
    }
    
    db.interview_questions.create_index([("user_id", ASCENDING)])
    db.interview_questions.create_index([("job_id", ASCENDING)])
    return interview_questions

def create_resume_analysis_collection():
    """Schema for storing detailed resume analysis"""
    resume_analysis = {
        "_id": str,  # ObjectId
        "user_id": str,  # UUID
        "resume_id": str,  # UUID
        "job_id": Optional[str],  # UUID, optional for job-specific analysis
        "analysis": {
            "skill_match": {
                "matched_skills": List[str],
                "missing_skills": List[str],
                "match_percentage": float
            },
            "experience_analysis": {
                "total_years": float,
                "relevant_years": float,
                "key_roles": List[str],
                "industry_experience": List[str]
            },
            "education_analysis": {
                "highest_degree": str,
                "relevant_courses": List[str],
                "education_match": float
            },
            "overall_score": float
        },
        "recommendations": {
            "suggested_improvements": List[str],
            "recommended_skills": List[str],
            "job_fit_analysis": str
        },
        "created_at": datetime,
        "updated_at": datetime
    }
    
    db.resume_analysis.create_index([("user_id", ASCENDING)])
    db.resume_analysis.create_index([("resume_id", ASCENDING)])
    db.resume_analysis.create_index([("job_id", ASCENDING)])
    return resume_analysis

def create_job_recommendations_collection():
    """Schema for storing job recommendations for users"""
    job_recommendations = {
        "_id": str,  # ObjectId
        "user_id": str,  # UUID
        "recommendations": [
            {
                "job_title": str,
                "company": str,
                "location": str,
                "match_score": float,
                "job_link": str,
                "source": str,
                "skills_matched": List[str],
                "skills_missing": List[str]
            }
        ],
        "recommendation_criteria": {
            "skills_prioritized": List[str],
            "location_preference": str,
            "experience_level": str,
            "job_type": str
        },
        "created_at": datetime
    }
    
    db.job_recommendations.create_index([("user_id", ASCENDING)])
    return job_recommendations

def create_resume_job_matches_collection():
    """Schema for storing job descriptions that match a particular resume"""
    resume_job_matches = {
        "_id": str,  # ObjectId
        "resume_id": int,  # Reference to resume_id
        "user_id": int,  # Reference to user_id
        
        # Match summary
        "match_summary": {
            "total_matches": int,
            "high_matches": int,  # Number of high-quality matches
            "medium_matches": int,  # Number of medium-quality matches
            "low_matches": int,  # Number of low-quality matches
            "last_updated": datetime
        },
        
        # List of matching job descriptions
        "matching_jobs": [
            {
                "job_id": int,  # Reference to job_id
                "title": str,
                "company_name": str,
                "match_score": float,  # Overall match score (0-100)
                "match_date": datetime,  # When this match was calculated
                
                # Match details
                "match_details": {
                    "skill_match": {
                        "score": float,
                        "matched_skills": List[str],
                        "missing_skills": List[str],
                        "skill_importance": Dict[str, float]  # Skill name to importance score
                    },
                    "experience_match": {
                        "score": float,
                        "years_match": float,  # How well years of experience match
                        "domain_match": float,  # How well domain experience matches
                        "role_match": float  # How well previous roles match
                    },
                    "education_match": {
                        "score": float,
                        "degree_match": float,
                        "field_match": float
                    }
                },
                
                # Job description data (subset of the full job description)
                "job_data": {
                    "description": str,
                    "key_responsibilities": List[str],
                    "required_skills": List[str],
                    "preferred_skills": List[str],
                    "experience_required": str,
                    "education_required": str,
                    "location": str,
                    "job_type": str,
                    "salary_range": Dict[str, float],  # {"min": value, "max": value}
                    "application_url": str
                },
                
                # AI-generated insights about this match
                "match_insights": {
                    "strengths": List[str],  # Areas where the candidate is strong
                    "gaps": List[str],  # Areas where the candidate has gaps
                    "recommendations": List[str],  # Recommendations for the candidate
                    "interview_preparation": List[str]  # Tips for interview preparation
                }
            }
        ],
        
        # Search criteria used for finding these matches
        "search_criteria": {
            "skills_prioritized": List[str],
            "experience_level": str,
            "job_types": List[str],
            "locations": List[str],
            "industries": List[str],
            "remote_preference": bool
        },
        
        # Timestamps
        "created_at": datetime,
        "updated_at": datetime
    }
    
    # Create indexes for efficient querying
    db.resume_job_matches.create_index([("resume_id", ASCENDING)])
    db.resume_job_matches.create_index([("user_id", ASCENDING)])
    db.resume_job_matches.create_index([("matching_jobs.job_id", ASCENDING)])
    db.resume_job_matches.create_index([("matching_jobs.match_score", ASCENDING)])
    
    return resume_job_matches

def create_resume_insights_collection():
    """Schema for storing resume insights"""
    resume_insights = {
        "resume_id": int,
        "candidate_id": int,
        "job_id": int,
        "parsed_text": str,
        "keywords_extracted": List[str],
        "skills_identified": List[str],
        "experience_analysis": {
            "years_experience": float,
            "relevant_experience": str,
            "key_achievements": List[str],
        },
        "education_analysis": {
            "degrees": List[str],
            "institutions": List[str],
            "graduation_years": List[int],
        },
        "insights": {
            "strengths": str,
            "weaknesses": str,
            "recommendations": str,
            "skill_match_score": float,
        },
        "ai_generated_qa": List[Dict[str, Any]],
        "generated_at": datetime,
    }
    db.resume_insights.create_index([("resume_id", ASCENDING)])
    return resume_insights

def create_job_description_collection():
    """Create a structured schema for storing job descriptions"""
    job_description_schema = {
        "job_id": int,  # Reference to SQL job_id
        "title": str,
        "source_file": str,
        "upload_date": datetime,
        "status": str,  # Active, Archived, etc.
        
        # Structured sections from the JD
        "sections": {
            "career_level_summary": str,
            "critical_competencies": List[Dict[str, str]],  # [{"name": "Systems Thinking", "description": "..."}]
            "key_responsibilities": List[str],
            "person_specification": {
                "knowledge": List[str],
                "skills": List[str],
                "education": List[str],
                "experience": List[str],
                "physical_demands": List[str]
            }
        },
        
        # Job metadata
        "metadata": {
            "role": str,  # e.g., "Site Reliability Engineer"
            "industry_type": str,  # e.g., "IT Services & Consulting"
            "department": str,  # e.g., "Engineering - Software & QA"
            "employment_type": str,  # e.g., "Full Time, Permanent"
            "role_category": str,  # e.g., "DevOps"
            "education_level": {
                "undergraduate": str,  # e.g., "Any Graduate"
                "postgraduate": str  # e.g., "Any Postgraduate"
            }
        },
        
        # Extracted skills and keywords
        "key_skills": List[str],  # List of all key skills mentioned
        "technical_skills": List[str],  # Specifically technical skills
        "soft_skills": List[str],  # Soft/interpersonal skills
        
        # AI analysis results
        "ai_analysis": {
            "role_importance": Dict[str, float],  # Importance scores for different aspects
            "required_skills": List[Dict[str, Any]],  # [{"name": "Python", "importance": 80, "selection_score": 75, "rejection_score": 85}]
            "experience_requirements": {
                "min_years": float,
                "preferred_years": float,
                "domains": List[str]  # Specific domains where experience is needed
            },
            "education_requirements": {
                "min_degree": str,
                "preferred_degree": str,
                "fields": List[str]  # Fields of study
            },
            "culture_fit_indicators": List[str],  # Cultural aspects mentioned in JD
            "summary": str  # AI-generated summary of the position
        },
        
        # Timestamps
        "created_at": datetime,
        "updated_at": datetime,
        "analyzed_at": Optional[datetime]
    }
    
    # Create index on job_id for fast lookups
    db.job_descriptions.create_index([("job_id", ASCENDING)])
    db.job_descriptions.create_index([("title", "text"), ("sections.key_responsibilities", "text")])
    
    return job_description_schema

def create_jd_insights_collection():
    """Schema for storing JD insights and analysis"""
    jd_insights = {
        "job_id": int,  # Reference to SQL job_id
        "title": str,  # Job title
        "created_at": datetime,
        
        # Extracted text sections
        "parsed_sections": {
            "career_summary": str,
            "competencies": str,
            "responsibilities": str,
            "knowledge_requirements": str,
            "skill_requirements": str,
            "education_requirements": str,
            "experience_requirements": str,
            "physical_demands": str,
            "role_metadata": str  # Role, industry, department, etc.
        },
        
        # Extracted entities
        "extracted_entities": {
            "job_titles": List[str],
            "skills": List[str],
            "technologies": List[str],
            "certifications": List[str],
            "education_levels": List[str],
            "experience_levels": List[str],
            "locations": List[str],
            "companies": List[str],
            "industries": List[str]
        },
        
        # Skill analysis
        "skill_analysis": {
            "technical_skills": List[Dict[str, Any]],  # [{"name": "Python", "importance": 80, "selection_weight": 75, "rejection_weight": 85}]
            "soft_skills": List[Dict[str, Any]],
            "domain_knowledge": List[Dict[str, Any]],
            "certifications": List[Dict[str, Any]]
        },
        
        # Role requirements analysis
        "role_requirements": {
            "seniority_level": str,  # Junior, Mid, Senior, Lead, etc.
            "min_experience_years": float,
            "preferred_experience_years": float,
            "min_education": str,
            "preferred_education": str,
            "required_certifications": List[str],
            "management_responsibilities": bool,
            "team_size": int  # If management role
        },
        
        # Competitive analysis
        "market_analysis": {
            "similar_roles": List[str],
            "average_salary_range": Dict[str, float],  # {"min": 80000, "max": 120000}
            "demand_level": str,  # High, Medium, Low
            "supply_level": str,  # High, Medium, Low
            "trending_skills": List[str]
        },
        
        # AI-generated insights
        "ai_insights": {
                        "role_summary": str,
            "key_differentiators": List[str],
            "suggested_interview_questions": List[Dict[str, str]],  # [{"question": "...", "purpose": "..."}]
            "evaluation_criteria": List[Dict[str, Any]],
            "candidate_sourcing_suggestions": List[str],
            "role_challenges": List[str],
            "success_indicators": List[str]
        },
        
        # Generated at timestamp
        "generated_at": datetime,
        "updated_at": datetime
    }
    
    # Create index on job_id for fast lookups
    db.jd_insights.create_index([("job_id", ASCENDING)])
    db.jd_insights.create_index([("title", "text")])
    
    return jd_insights

def create_decision_making_insights_collection():
    """Schema for storing comprehensive decision-making insights"""
    decision_making_insights = {
        "decision_id": int,
        "job_id": int,
        "candidate_id": int,
        "resume_id": int,
        "created_by": int,  # User ID who initiated the decision
        "decision_date": datetime,
        
        # Decision context
        "decision_context": {
            "stage": str,  # "Initial Screening", "Technical Interview", "Final Round", etc.
            "position": str,
            "department": str,
            "urgency_level": str,  # "Low", "Medium", "High"
            "hiring_goals": List[str]
        },
        
        # Candidate assessment
        "candidate_assessment": {
            "resume_score": float,
            "interview_scores": List[Dict[str, Any]],  # Multiple interview scores
            "skill_match": float,
            "experience_relevance": float,
            "cultural_fit": float,
            "overall_rating": float
        },
        
        # Decision factors
        "decision_factors": {
            "technical_competence": {
                "weight": float,
                "score": float,
                "justification": str
            },
            "experience": {
                "weight": float,
                "score": float,
                "justification": str
            },
            "cultural_fit": {
                "weight": float,
                "score": float,
                "justification": str
            },
            "communication_skills": {
                "weight": float,
                "score": float,
                "justification": str
            },
            "growth_potential": {
                "weight": float,
                "score": float,
                "justification": str
            },
            "additional_factors": List[Dict[str, Any]]
        },
        
        # Decision recommendation
        "recommendation": {
            "decision": str,  # "Hire", "Reject", "Hold", "Additional Interview"
            "confidence": float,  # 0-100
            "justification": str,
            "considerations": List[str],
            "risks": List[str],
            "next_steps": List[str]
        },
        
        # Comparison with other candidates
        "comparative_analysis": {
            "candidate_ranking": int,  # Rank among all candidates
            "percentile": float,  # Percentile among all candidates
            "strengths_vs_others": List[str],
            "weaknesses_vs_others": List[str],
            "unique_value_proposition": str
        },
        
        # AI insights
        "ai_insights": {
            "bias_detection": {
                "detected_biases": List[str],
                "mitigation_suggestions": List[str]
            },
            "decision_quality": float,  # 0-100
            "confidence_score": float,  # 0-100
            "alternative_perspectives": List[str],
            "long_term_fit_prediction": float  # 0-100
        },
        
        # Decision status
        "status": str,  # "Draft", "Pending", "Approved", "Rejected"
        "approvers": List[int],  # User IDs of approvers
        "comments": List[Dict[str, Any]],  # Comments from reviewers
        
        # Original data from resume analysis
        "resume_data": {
            "parsed_text": str,
            "keywords_extracted": List[str],
            "skills_identified": List[str],
            "experience_analysis": {
                "years_experience": float,
                "relevant_experience": str,
                "key_achievements": List[str],
            },
            "education_analysis": {
                "degrees": List[str],
                "institutions": List[str],
                "graduation_years": List[int],
            }
        },
        
        # Timestamps
        "created_at": datetime,
        "updated_at": datetime,
        "generated_at": datetime
    }
    
    # Create indexes for efficient querying
    db.decision_making_insights.create_index([("decision_id", ASCENDING)])
    db.decision_making_insights.create_index([("job_id", ASCENDING)])
    db.decision_making_insights.create_index([("candidate_id", ASCENDING)])
    db.decision_making_insights.create_index([("resume_id", ASCENDING)])
    db.decision_making_insights.create_index([("created_at", ASCENDING)])
    
    return decision_making_insights

def create_candidate_evaluation_collection():
    """Schema for storing candidate evaluations after interviews"""
    candidate_eval = {
        "evaluation_id": int,  # Unique identifier for this evaluation
        "candidate_id": int,  # Reference to SQL candidate_id
        "job_id": int,  # Reference to SQL job_id
        "interview_id": int,  # Reference to the interview session
        "evaluator_id": int,  # User who conducted the interview (if applicable)
        "evaluation_date": datetime,
        
        # Overall scores
        "overall_scores": {
            "total_score": float,  # 0-100 overall score
            "recommendation": str,  # "Hire", "Reject", "Consider"
            "match_percentage": float,  # How well candidate matches JD
            "confidence_score": float  # AI confidence in evaluation
        },
        
        # Detailed scoring categories
        "category_scores": {
            "technical_skills": {
                "score": float,  # 0-100
                "strengths": List[str],
                "weaknesses": List[str],
                "detailed_skills": Dict[str, float]  # Individual skill scores
            },
            "experience": {
                "score": float,
                "relevance": float,
                "depth": float,
                "breadth": float,
                "achievements": List[str]
            },
            "communication": {
                "score": float,
                "clarity": float,
                "articulation": float,
                "listening": float,
                "non_verbal": float
            },
            "problem_solving": {
                "score": float,
                "approach": float,
                "creativity": float,
                "analytical_thinking": float
            },
            "cultural_fit": {
                "score": float,
                "alignment": float,
                "adaptability": float,
                "teamwork": float
            }
        },
        
        # Question-by-question analysis
        "question_analysis": List[Dict[str, Any]],
        
        # AI-generated feedback
        "ai_feedback": {
            "summary": str,  # Overall evaluation summary
            "strengths": List[str],  # Key strengths identified
            "areas_for_improvement": List[str],
            "hiring_recommendation": str,  # Detailed recommendation
            "fit_analysis": str,  # Analysis of fit for the role
            "suggested_interview_questions": List[str]  # For follow-up interviews
        },
        
        # Skill assessment against job requirements
        "skill_assessment": {
            "required_skills": List[str],
            "matched_skills": List[str],
            "missing_skills": List[str],
            "skill_scores": Dict[str, float],
            "skill_gap_analysis": str
        },
        
        # Metadata
        "evaluation_type": str,  # "AI", "Human", "Hybrid"
        "evaluation_method": str,  # "Video", "Audio", "Text"
        "evaluation_duration": float,  # In minutes
        "notes": str,  # Additional notes
        "status": str,  # "Draft", "Completed", "Reviewed"
        
        # Timestamps
        "created_at": datetime,
        "updated_at": datetime
    }
    # Create indexes for fast lookups
    db.candidate_evaluations.create_index([("candidate_id", ASCENDING)])
    db.candidate_evaluations.create_index([("evaluation_id", ASCENDING)])
    db.candidate_evaluations.create_index([("job_id", ASCENDING)])
    db.candidate_evaluations.create_index([("interview_id", ASCENDING)])
    

    return candidate_eval

def create_interview_transcription_collection():
    interview_transcription = {
        "interview_id": int,
        "candidate_id": int,
        "job_id": int,
        "resume_id": int,
        "transcription": {
            "full_text": str,
            "segments": List[Dict],
            "duration": float,
            "speaker_segments": List[Dict],
        },
        "analysis": {
            "sentiment_analysis": {
                "overall_sentiment": float,
                "sentiment_breakdown": Dict[str, float],
                "key_moments": List[Dict],
            },
            "technical_analysis": {
                "technical_accuracy": float,
                "concept_understanding": float,
                "problem_solving": float,
            },
            "communication_analysis": {
                "clarity": float,
                "confidence": float,
                "articulation": float,
            },
        },
        "keywords_extracted": List[str],
        "created_at": datetime,
    }
    db.interview_transcriptions.create_index([("interview_id", ASCENDING)])
    return interview_transcription


def create_qa_scoring_rubrics_collection():
    """Schema for storing Q/A scoring rubrics and mapping criteria"""
    qa_scoring_rubrics = {
        "rubric_id": int,
        "job_id": int,  # Optional, for job-specific rubrics
        "name": str,
        "description": str,
        "question_type": str,  # "Technical", "Behavioral", "Coding", "Domain-specific"
        
        # Scoring criteria
        "scoring_criteria": List[Dict[str, Any]],  # List of criteria with weights
        
        # Score mapping
        "score_mapping": {
            "excellent": {
                "min_score": float,
                "max_score": float,
                "description": str
            },
            "good": {
                "min_score": float,
                "max_score": float,
                "description": str
            },
            "average": {
                "min_score": float,
                "max_score": float,
                "description": str
            },
            "below_average": {
                "min_score": float,
                "max_score": float,
                "description": str
            },
            "poor": {
                "min_score": float,
                "max_score": float,
                "description": str
            }
        },
        
        # Weighting system
        "question_weights": Dict[str, float],  # Weights for different question aspects
        
        # Created/updated timestamps
        "created_at": datetime,
        "updated_at": datetime
    }
    
    db.qa_scoring_rubrics.create_index([("rubric_id", ASCENDING)])
    db.qa_scoring_rubrics.create_index([("job_id", ASCENDING)])
    
    return qa_scoring_rubrics

def create_coding_evaluation_collection():
    """Schema for storing coding-specific evaluations"""
    coding_evaluation = {
        "evaluation_id": int,
        "candidate_id": int,
        "job_id": int,
        "question_id": int,
        
        # Code submission
        "code_submission": {
            "language": str,
            "code_text": str,
            "submission_time": datetime
        },
        
        # Execution results
        "execution_results": {
            "status": str,  # "Success", "Error", "Timeout"
            "output": str,
            "runtime": float,  # in milliseconds
            "memory_usage": float,  # in KB
            "test_cases_passed": int,
            "total_test_cases": int
        },
        
        # Code quality metrics
        "code_quality": {
            "correctness": float,  # 0-100
            "efficiency": float,  # 0-100
            "code_style": float,  # 0-100
            "readability": float,  # 0-100
            "maintainability": float,  # 0-100
            "error_handling": float  # 0-100
        },
        
        # AI analysis
        "ai_analysis": {
            "strengths": List[str],
            "weaknesses": List[str],
            "suggestions": List[str],
            "alternative_approaches": List[str],
            "complexity_analysis": str
        },
        
        # Overall score
        "overall_score": float,  # 0-100
        
        # Timestamps
        "created_at": datetime,
        "updated_at": datetime
    }
    
    db.coding_evaluations.create_index([("evaluation_id", ASCENDING)])
    db.coding_evaluations.create_index([("candidate_id", ASCENDING)])
    db.coding_evaluations.create_index([("job_id", ASCENDING)])
    
    return coding_evaluation

def create_candidate_comparison_collection():
    """Schema for storing candidate comparison data"""
    candidate_comparison = {
        "comparison_id": int,
        "job_id": int,
        "created_by": int,  # User ID
        "comparison_date": datetime,
        
        # Candidates being compared
        "candidates": List[Dict[str, Any]],  # List of candidate summaries
        
        # Comparison metrics
        "comparison_metrics": {
            "technical_skills": Dict[int, float],  # candidate_id: score
            "communication": Dict[int, float],
            "problem_solving": Dict[int, float],
            "experience_relevance": Dict[int, float],
            "cultural_fit": Dict[int, float],
            "overall_ranking": Dict[int, int]  # candidate_id: rank
        },
        
        # Question-by-question comparison
        "question_comparisons": List[Dict[str, Any]],  # Detailed comparison by question
        
        # AI insights
        "ai_insights": {
            "strongest_candidate": int,  # candidate_id
            "justification": str,
            "candidate_strengths": Dict[int, List[str]],  # candidate_id: strengths
            "candidate_weaknesses": Dict[int, List[str]],  # candidate_id: weaknesses
            "hiring_recommendations": Dict[int, str]  # candidate_id: recommendation
        },
        
        # Timestamps
        "created_at": datetime,
        "updated_at": datetime
    }
    
    db.candidate_comparisons.create_index([("comparison_id", ASCENDING)])
    db.candidate_comparisons.create_index([("job_id", ASCENDING)])
    
    return candidate_comparison

def create_ai_questions_collection():
    ai_questions = {
        "job_id": int,
        "questions": List[Dict],
        "question_categories": {
            "technical": List[Dict],
            "behavioral": List[Dict],
            "experience": List[Dict],
            "role_specific": List[Dict],
        },
        "difficulty_distribution": {
                        "easy": List[int],
            "medium": List[int],
            "hard": List[int],
        },
        "generated_at": datetime,
        "last_updated": datetime,
    }
    db.ai_generated_questions.create_index([("job_id", ASCENDING)])
    return ai_questions

def initialize_mongodb_collections():
    """Initialize all MongoDB collections with their schemas"""
    collections = {
        "parsed_resumes": create_parsed_resumes_collection(),
        "ai_responses": create_ai_responses_collection(),
        "job_search_logs": create_job_search_logs_collection(),
        "interview_questions": create_interview_questions_collection(),
        "resume_analysis": create_resume_analysis_collection(),
        "job_recommendations": create_job_recommendations_collection(),
        "resume_job_matches": create_resume_job_matches_collection(),
        "job_descriptions": create_job_description_collection(),
        "jd_insights": create_jd_insights_collection(),
        "decision_making_insights": create_decision_making_insights_collection(),
        "candidate_evaluations": create_candidate_evaluation_collection(),
        "interview_transcriptions": create_interview_transcription_collection(),
        "resume_insights": create_resume_insights_collection(),
        "qa_scoring_rubrics": create_qa_scoring_rubrics_collection(),
        "coding_evaluations": create_coding_evaluation_collection(),
        "candidate_comparisons": create_candidate_comparison_collection(),
        "ai_generated_questions": create_ai_questions_collection()
    }

    print("\nInitializing MongoDB Collections...")
    for name, schema in collections.items():
        print(f"✓ Initialized {name} collection")
    
    return collections

# Create all collections when module is imported
MONGODB_SCHEMAS = initialize_mongodb_collections()


