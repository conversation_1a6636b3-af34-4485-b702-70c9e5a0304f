"""
Database Schema Definitions

This module contains SQLAlchemy ORM models for the application.
Models are organized into logical groups for better maintainability.
"""
import uuid
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel
from enum import Enum

class ExperienceLevel(str, Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    EXPERT = "expert"


from sqlalchemy import (
    Boolean, Column, DateTime, Float, ForeignKey, Integer, String, Text, JSON, 
    Enum as SQLAlchemyEnum, func, Index, Table, and_, Numeric
)
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from database import Base, engine

# Re-export for easier imports in other modules
__all__ = [
    'Role', 'User', 'UserProfile', 'Resume', 'JobDescription', 'Candidate',
    'ResumeJobAssociation', 'JobApplication', 'SimulationInterview',
    'SalaryPrediction', 'ThresholdScore', 'ResumeEvaluation', 'ResumeAnalytics',
    'Skill', 'JobRequiredSkills', 'UserSkill', 'LearningPath', 'LearningProgress'
]

"""
Core User Management Models
"""

class Role(Base):
    """User roles in the system (e.g., candidate, employer, admin)."""
    __tablename__ = "roles"
    
    role_id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(20), unique=True, nullable=False)  # "candidate" or "employer"
    description = Column(String(255), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    users = relationship("User", back_populates="role_rel")
    
    def __repr__(self) -> str:
        return f"<Role {self.name}>"

class User(Base):
    """Main user account in the system."""
    __tablename__ = "users"
    
    user_id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=False)
    initials = Column(String(10), nullable=True)
    email = Column(String(255), unique=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    phone_number = Column(String(15), nullable=True)
    role_id = Column(Integer, ForeignKey("roles.role_id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    role_rel = relationship("Role", back_populates="users")
    resumes = relationship("Resume", back_populates="user", cascade="all, delete")
    applications = relationship("JobApplication", back_populates="user", cascade="all, delete")
    interviews = relationship(
        "SimulationInterview",
        back_populates="user",
        foreign_keys="SimulationInterview.user_id"
    )
    predictions = relationship("SalaryPrediction", back_populates="user", cascade="all, delete")
    threshold_scores = relationship("ThresholdScore", back_populates="user", cascade="all, delete")
    candidate_profile = relationship(
        "Candidate", 
        back_populates="user", 
        uselist=False, 
        cascade="all, delete"
    )
    user_profile = relationship(
        "UserProfile", 
        back_populates="user", 
        uselist=False, 
        cascade="all, delete"
    )
    learning_progress = relationship("LearningProgress", back_populates="user", cascade="all, delete")
    analytics = relationship("CandidateAnalytics", back_populates="user", cascade="all, delete")
    skill_gap_analyses = relationship("SkillGapAnalysis", back_populates="user", cascade="all, delete")
    
    def __repr__(self) -> str:
        return f"<User {self.email}>"

class UserProfile(Base):
    """Extended user profile information."""
    __tablename__ = "user_profiles"
    
    profile_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    
    # Basic Information
    job_title = Column(String(255), nullable=True)
    location = Column(String(255), nullable=True)
    
    # Profile Status
    status = Column(String(50), nullable=True)  # e.g., "Premium Member"
    profile_strength = Column(Float, nullable=True)
    profile_completion = Column(Float, nullable=True)  # percentage
    profile_score = Column(Float, nullable=True)  # percentage
    overall_competitiveness = Column(String(50), nullable=True)
    
    # Section Status
    work_experience_status = Column(String(50), nullable=True)
    portfolio_status = Column(String(50), nullable=True)
    basic_info_status = Column(String(50), nullable=True)
    resume_status = Column(String(50), nullable=True)
    skills_status = Column(String(50), nullable=True)
    
    
    
    # Professional Information
    experience_level = Column(Float, nullable=True)  # years
    skill_count = Column(Integer, nullable=True)
    education_level = Column(String(100), nullable=True)
    expected_salary = Column(String(50), nullable=True)
    current_salary = Column(String(50), nullable=True)
    
    # Verification
    verification_type = Column(String(100), nullable=True)  # e.g., 'employment', 'education'
    verification_date = Column(DateTime, nullable=True)
    expiration_date = Column(DateTime, nullable=True)
    verified_by = Column(String(255), nullable=True)
    document_url = Column(String(255), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="user_profile")
    
    def __repr__(self) -> str:
        return f"<UserProfile user_id={self.user_id}>"

"""
Resume and Job Application Models
"""

class Resume(Base):
    """User's resume/CV document with associated metadata."""
    __tablename__ = "resumes"
    
    resume_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    candidate_id = Column(Integer, ForeignKey("candidates.candidate_id", ondelete="CASCADE"), nullable=False)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="SET NULL"), nullable=True)
    
    # File Information
    file_url = Column(Text, nullable=False)
    resume_url = Column(String(255), nullable=True)
    file_name = Column(String(255), nullable=True)
    file_type = Column(String(50), nullable=True)
    file_size_limit = Column(String(50), nullable=True)
    
    # Processing Information
    parsed_data = Column(JSONB, nullable=True)
    ai_resume_generation_status = Column(String(50), nullable=True)
    ai_resume_time_estimate = Column(String(50), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True)
    version = Column(Integer, default=1)
    activity_type = Column(String(50), nullable=True)
    
    # Timestamps
    upload_date = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="resumes")
    candidate = relationship("Candidate", back_populates="resumes")
    applications = relationship("JobApplication", back_populates="resume", cascade="all, delete")
    evaluations = relationship("ResumeEvaluation", back_populates="resume", cascade="all, delete")
    analytics = relationship("ResumeAnalytics", back_populates="resume", cascade="all, delete")
    job_associations = relationship("ResumeJobAssociation", back_populates="resume")
    
    def __repr__(self) -> str:
        return f"<Resume {self.file_name} (User: {self.user_id})>"

class JobDescription(Base):
    """Job posting details and requirements."""
    __tablename__ = "job_descriptions"
    
    job_id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Job Information
    title = Column(String(255), nullable=False)
    company_name = Column(String(255), nullable=True)
    location = Column(String(255), nullable=True)
    department = Column(String(100), nullable=True)
    
    # Job Details
    description = Column(Text, nullable=False)
    raw_text = Column(Text, nullable=True)
    keywords = Column(JSONB, nullable=True)  # Storing as JSON array
    
    # Requirements
    experience_level = Column(String(100), nullable=True)
    education_requirements = Column(Text, nullable=True)
    required_skills = Column(JSONB, nullable=True)  # Storing as JSON array
    
    # Status and Metadata
    status = Column(String(50), default="Active")  # Active, Closed, Draft, etc.
    priority = Column(String(50), nullable=True)  # High, Medium, Low
    source = Column(String(255), nullable=True)
    source_file = Column(String(255), nullable=True)
    
    # Compensation
    salary_range = Column(String(50), nullable=True)  # e.g., "50000-70000"
    
    # Analytics
    threshold_score = Column(Float, nullable=True)
    applications_received = Column(Integer, default=0)
    
    # Timestamps
    posted_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    applications = relationship("JobApplication", back_populates="job", cascade="all, delete")
    candidates = relationship("Candidate", back_populates="job")
    required_skills_rel = relationship("JobRequiredSkills", back_populates="job", cascade="all, delete-orphan")
    skill_gap_analyses = relationship("SkillGapAnalysis", back_populates="job")
    threshold_scores = relationship("ThresholdScore", back_populates="job", cascade="all, delete")
    resume_associations = relationship("ResumeJobAssociation", back_populates="job")
    company_details = relationship("CompanyDetails", back_populates="job", uselist=False)
    
    def __repr__(self) -> str:
        return f"<Job {self.title} at {self.company_name}>"

class Candidate(Base):
    """Candidate information and application details."""
    __tablename__ = "candidates"
    
    # Primary key
    candidate_id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Foreign keys
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="SET NULL"), nullable=True)
    
    # ===== Basic Information =====
    name = Column(String(255), nullable=True)
    email = Column(String(255), nullable=True, unique=True)
    phone = Column(String(20), nullable=True)
    photo_url = Column(String(255), nullable=True)
    
    # ===== Professional Information =====
    company = Column(String(255), nullable=True)  # Current/last company
    education = Column(Text, nullable=True)  # Highest qualification
    
    # ===== Application Status =====
    status = Column(String(50), default="New")  # New, In Review, Shortlisted, Rejected, Hired
    
    # ===== Resume and Interview =====
    resume_url = Column(String(255), nullable=True)
    interview_score = Column(Float, nullable=True)
    
    # ===== Evaluation Scores =====
    match_score = Column(Float, nullable=True, comment='How closely the candidate matches job requirements (0-100)')
    score_breakdown = Column(JSON, nullable=True, comment='Detailed breakdown of scores by category (technical, communication, etc.)')
    
    # ===== Background Verification =====
    background_verification_status = Column(String(50), nullable=True)
    background_verification_date = Column(DateTime, nullable=True)
    background_verification_details = Column(JSON, nullable=True)
    
    # ===== Availability =====
    notice_period = Column(String(50), nullable=True)  # Notice period in days/weeks
    location = Column(String(255), nullable=True)
    available_times = Column(JSON, nullable=True)  # Available time slots
    
    # ===== Authentication Status =====
    auth_status = Column(String(50), nullable=True)
    
    # ===== Document Management =====
    # DigiLocker Integration
    digilocker_user_id = Column(String(255), nullable=True)
    document_type = Column(String(100), nullable=True)
    document_id = Column(String(100), nullable=True)
    document_name = Column(String(255), nullable=True)
    issuer = Column(String(255), nullable=True)
    issue_date = Column(DateTime, nullable=True)
    expiry_date = Column(DateTime, nullable=True)
    verification_status = Column(String(50), nullable=True)
    document_data = Column(JSON, nullable=True)
    
    # Offer Documents
    offer_filename = Column(String(255), nullable=True)
    offer_original_filename = Column(String(255), nullable=True)
    offer_file_size = Column(Integer, nullable=True)
    offer_content_type = Column(String(100), nullable=True)
    offer_upload_status = Column(String(50), nullable=True)
    offer_notes = Column(Text, nullable=True)
    
    # Contract Documents
    contract_filename = Column(String(255), nullable=True)
    contract_original_filename = Column(String(255), nullable=True)
    contract_file_size = Column(Integer, nullable=True)
    contract_content_type = Column(String(100), nullable=True)
    contract_upload_status = Column(String(50), nullable=True)
    contract_notes = Column(Text, nullable=True)
    
    # Negotiation Documents
    negotiation_filename = Column(String(255), nullable=True)
    negotiation_original_filename = Column(String(255), nullable=True)
    negotiation_file_size = Column(Integer, nullable=True)
    negotiation_content_type = Column(String(100), nullable=True)
    negotiation_upload_status = Column(String(50), nullable=True)
    negotiation_notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="candidate_profile")
    job = relationship("JobDescription", back_populates="candidates")
    resumes = relationship("Resume", back_populates="candidate", cascade="all, delete-orphan")
    threshold_scores = relationship("ThresholdScore", back_populates="candidate")
    
    def __repr__(self) -> str:
        return f"<Candidate {self.name} ({self.candidate_id})>"

class ResumeJobAssociation(Base):
    """Association table between resumes and jobs with matching scores."""
    __tablename__ = "resume_job_associations"
    
    id = Column(Integer, primary_key=True, index=True)
    resume_id = Column(Integer, ForeignKey("resumes.resume_id"), nullable=False)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id"), nullable=False)
    
    # Matching Scores
    match_score = Column(Float, nullable=True)  # Overall matching score (0-100)
    selection_percentage = Column(Float, nullable=True)  # Chance of being selected
    rejection_percentage = Column(Float, nullable=True)  # Chance of being rejected
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    resume = relationship("Resume", back_populates="job_associations")
    job = relationship("JobDescription", back_populates="resume_associations")
    
    def __repr__(self) -> str:
        return f"<ResumeJobAssociation resume_id={self.resume_id} job_id={self.job_id} score={self.match_score}>"

"""
Application and Interview Models
"""

class JobApplication(Base):
    """Tracks job applications submitted by candidates."""
    __tablename__ = "job_applications"
    
    application_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="CASCADE"))
    resume_id = Column(Integer, ForeignKey("resumes.resume_id", ondelete="CASCADE"))
    
    # Application Details
    status = Column(String(20), default="applied")  # applied, reviewed, interviewed, offered, hired, rejected
    note = Column(Text, nullable=True)
    
    # Timestamps
    applied_at = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="applications")
    job = relationship("JobDescription", back_populates="applications")
    resume = relationship("Resume", back_populates="applications")
    timeline = relationship("ApplicationTimeline", back_populates="application", cascade="all, delete")
    
    def __repr__(self) -> str:
        return f"<JobApplication {self.application_id} (User: {self.user_id}, Job: {self.job_id})>"

class SimulationInterview(Base):
    """Records of simulation interviews conducted with candidates."""
    __tablename__ = "simulation_interviews"
    
    interview_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="CASCADE"), nullable=False)
    candidate_id = Column(Integer, ForeignKey("candidates.candidate_id", ondelete="CASCADE"), nullable=False)
    
    # Interview Details
    performance_score = Column(Numeric(5, 2), nullable=True)  # Using Numeric type as per DB
    feedback = Column(Text, nullable=True)
    status = Column(String(50), nullable=True)  # shortlisted, rejected, etc.
    
    # Timestamps
    created_at = Column(DateTime, nullable=True, default=datetime.utcnow)
    shortlisted_at = Column(DateTime, nullable=True)  # When candidate was shortlisted
    
    # Relationships
    user = relationship(
        "User",
        back_populates="interviews",
        foreign_keys=[user_id]
    )
    candidate = relationship("Candidate")
    job = relationship("JobDescription")
    
    def __repr__(self) -> str:
        return f"<SimulationInterview {self.interview_id} (User: {self.user_id}, Status: {self.status})>"

"""
Analytics and Prediction Models
"""

class SalaryPrediction(Base):
    """Salary predictions based on role, skills, and experience."""
    __tablename__ = "salary_predictions"
    
    prediction_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    
    # Prediction Inputs
    role = Column(String(255), nullable=True)
    skills = Column(JSONB, nullable=True)  # Array of skills
    years_of_experience = Column(Integer, nullable=True)
    
    # Prediction Output
    predicted_salary = Column(String(50), nullable=True)  # Could be a range like "70,000-90,000"
    confidence_score = Column(Float, nullable=True)  # 0-1 confidence level
    
    # Metadata
    model_version = Column(String(50), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="predictions")
    
    def __repr__(self) -> str:
        return f"<SalaryPrediction {self.prediction_id} (User: {self.user_id}, Salary: {self.predicted_salary})>"

class ThresholdScore(Base):
    """Scoring thresholds for candidate evaluation and matching."""
    __tablename__ = "threshold_scores"
    
    threshold_id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Entity References
    user_id = Column(Integer, ForeignKey("users.user_id"), nullable=True)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id"), nullable=True)
    resume_id = Column(Integer, ForeignKey("resumes.resume_id"), nullable=True)
    candidate_id = Column(Integer, ForeignKey("candidates.candidate_id"), nullable=True)
    
    # Score Values
    selection_score = Column(Float, nullable=True)  # 0-100 score for selection
    rejection_score = Column(Float, nullable=True)  # 0-100 score for rejection
    threshold_value = Column(Float, nullable=True)  # The threshold value used
    
    # Score Details
    threshold_result = Column(JSONB, nullable=True)  # Detailed results
    threshold_prompts = Column(Text, nullable=True)  # Prompts used for thresholding
    custom_prompts = Column(Text, nullable=True)  # Any custom prompts used
    
    # Metadata
    activity_type = Column(String(50), nullable=True)  # Type of activity that generated this score
    sample_prompts_history = Column(Text, nullable=True)  # History of prompts used
    threshold_history = Column(JSONB, nullable=True)  # Historical threshold data
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    job = relationship("JobDescription", back_populates="threshold_scores")
    user = relationship("User", back_populates="threshold_scores")
    resume = relationship("Resume")
    candidate = relationship("Candidate", back_populates="threshold_scores")
    
    def __repr__(self) -> str:
        return f"<ThresholdScore {self.threshold_id} (User: {self.user_id}, Job: {self.job_id})>"

"""
Evaluation and Feedback Models
"""

class ResumeEvaluation(Base):
    """Evaluations and feedback for candidate resumes."""
    __tablename__ = "resume_evaluations"
    
    evaluation_id = Column(Integer, primary_key=True, autoincrement=True)
    resume_id = Column(Integer, ForeignKey("resumes.resume_id"))
    evaluator_id = Column(Integer, ForeignKey("users.user_id"), nullable=True)
    
    # Evaluation Details
    evaluation_type = Column(String(50), nullable=False, default="automated")  # automated, manual
    score = Column(Float, nullable=True)  # 0-100 score
    
    # Feedback Components
    strengths = Column(Text, nullable=True)  # Key strengths
    weaknesses = Column(Text, nullable=True)  # Areas for improvement
    feedback = Column(Text, nullable=True)  # Overall feedback
    
    # Metadata
    activity_type = Column(String(50), nullable=True)  # Type of evaluation activity
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    resume = relationship("Resume", back_populates="evaluations")
    evaluator = relationship("User", foreign_keys=[evaluator_id])
    
    def __repr__(self) -> str:
        return f"<ResumeEvaluation {self.evaluation_id} (Resume: {self.resume_id}, Score: {self.score})>"

class ResumeAnalytics(Base):
    """Analytics and insights generated from resume analysis."""
    __tablename__ = "resume_analytics"
    
    analytics_id = Column(Integer, primary_key=True, autoincrement=True)
    resume_id = Column(Integer, ForeignKey("resumes.resume_id"), nullable=False)
    
    # Analysis Data
    ai_generated_question = Column(String(255), nullable=False)  # Question generated by AI
    answer_text = Column(Text, nullable=True)  # Answer provided by candidate
    
    # Evaluation Metrics
    score = Column(Float, nullable=True)  # 0-100 score
    strengths = Column(Text, nullable=True)  # Identified strengths
    weaknesses = Column(Text, nullable=True)  # Identified weaknesses
    
    # Insights
    insights = Column(Text, nullable=True)  # AI-generated insights
    recommendations = Column(Text, nullable=True)  # Suggested improvements
    
    # Metadata
    activity_type = Column(String(50), nullable=True)  # Type of analysis performed
    model_version = Column(String(50), nullable=True)  # Version of the AI model used
    
    # Timestamps
    generated_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    resume = relationship("Resume", back_populates="analytics")
    
    def __repr__(self) -> str:
        return f"<ResumeAnalytics {self.analytics_id} (Resume: {self.resume_id})>"

"""
Skills and Learning Models
"""

class JobRequiredSkills(Base):
    """Association table between JobDescription and Skill with additional metadata."""
    __tablename__ = "job_required_skills"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="CASCADE"))
    skill_id = Column(Integer, ForeignKey("skills.skill_id", ondelete="CASCADE"))
    
    # Additional metadata
    importance = Column(String(20), default="required")  # required, nice_to_have, etc.
    min_years = Column(Float, nullable=True)  # Minimum years of experience required
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    job = relationship("JobDescription", back_populates="required_skills_rel")
    skill = relationship("Skill", back_populates="job_skills")
    
    def __repr__(self) -> str:
        return f"<JobRequiredSkills Job: {self.job_id}, Skill: {self.skill_id}>"


class Skill(Base):
    """Skills that can be associated with users and jobs."""
    __tablename__ = "skills"
    
    skill_id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Skill Information
    skill_name = Column(String(255), nullable=False, unique=True, index=True)
    skill_type = Column(String(50), nullable=True)  # Technical, Soft, Domain, etc.
    category = Column(String(100), nullable=True)  # Programming, Database, Framework, etc.
    description = Column(Text, nullable=True)
    
    # For skill gap analysis
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=True)
    current_level = Column(Float, nullable=True)  # 0-100 scale
    required_level = Column(Float, nullable=True)  # 0-100 scale
    gap_score = Column(Float, nullable=True)  # required_level - current_level
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    job_skills = relationship("JobRequiredSkills", back_populates="skill")
    user_skills = relationship("UserSkill", back_populates="skill")
    skill_gap_analyses = relationship("SkillGapAnalysis", back_populates="skill")
    
    def __repr__(self) -> str:
        return f"<Skill {self.skill_name} (Type: {self.skill_type})>"
 # ---16.UserSkill---
class UserSkill(Base):
    """Skills associated with users and their proficiency levels."""
    __tablename__ = "user_skills"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    skill_id = Column(Integer, ForeignKey("skills.skill_id", ondelete="CASCADE"))
    
    # Skill Metrics
    proficiency_level = Column(Integer, default=1)  # 1-5 scale
    years_of_experience = Column(Float, default=0.0)  # Years of experience
    last_used_year = Column(Integer, nullable=True)  # Last year the skill was used
    
    # Verification
    is_verified = Column(Boolean, default=False)  # If the skill has been verified
    verified_by = Column(Integer, ForeignKey("users.user_id"), nullable=True)  # Who verified it
    verified_at = Column(DateTime, nullable=True)  # When it was verified
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    skill = relationship("Skill", back_populates="user_skills")
    verifier = relationship("User", foreign_keys=[verified_by])
    
    def __repr__(self) -> str:
        return f"<UserSkill User: {self.user_id}, Skill: {self.skill_id}, Level: {self.proficiency_level}>"

# ----17.LearningPath----
class LearningPath(Base):
    """Custom learning paths for skill development."""
    __tablename__ = "learning_paths"
    
    path_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    
    # Path Information
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Progress Tracking
    progress = Column(Float, default=0)  # 0-100 percentage
    step = Column(Integer, default=1)  # Current step in the path
    total_steps = Column(Integer, default=1)  # Total number of steps
    
    # Metadata
    difficulty = Column(String(50), nullable=True)  # Beginner, Intermediate, Advanced
    duration = Column(String(50), nullable=True)  # Estimated duration (e.g., "4 weeks")
    status = Column(String(50), default="Not Started")  # Not Started, In Progress, Completed, Paused
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User")
    
    def __repr__(self) -> str:
        return f"<LearningPath {self.title} (User: {self.user_id}, Progress: {self.progress}%)>"
  # ----18. LearningProgress----
class LearningProgress(Base):
    """Tracks user progress in learning and skill development."""
    __tablename__ = "learning_progress"
    
    progress_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    learning_path_id = Column(Integer, ForeignKey("learning_paths.path_id"), nullable=True)
    
    # Progress Metrics
    completion_rate = Column(Float, nullable=True)  # Overall completion percentage
    courses_completed = Column(Integer, default=0)  # Total courses completed
    study_hours = Column(Float, default=0.0)  # Total hours spent learning
    skills_improved = Column(Integer, default=0)  # Number of skills improved
    current_streak = Column(Integer, default=0)  # Current streak in days
    
    # Goals
    weekly_goal = Column(JSONB, nullable=True)  # {target_hours: number, target_courses: number}
    monthly_goal = Column(JSONB, nullable=True)  # {target_hours: number, target_skills: number}
    
    # Recommendations
    skill_recommendations = Column(JSONB, nullable=True)  # Recommended skills to learn
    current_learning = Column(JSONB, nullable=True)  # Currently learning items
    recommended_certifications = Column(JSONB, nullable=True)  # Recommended certs
    
    # Achievements
    achievement_points = Column(Integer, default=0)  # Total points earned
    badges_earned = Column(JSONB, nullable=True)  # List of earned badges
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="learning_progress")
    learning_path = relationship("LearningPath")
    
    def __repr__(self) -> str:
        return f"<LearningProgress User: {self.user_id}, Completion: {self.completion_rate}%>"

# --- 19. Application Timeline ---
class ApplicationTimeline(Base):
    __tablename__ = "application_timeline"
    timeline_id = Column(Integer, primary_key=True, autoincrement=True)
    application_id = Column(Integer, ForeignKey("job_applications.application_id", ondelete="CASCADE"))
    stage = Column(String(50), nullable=True)  # Maps to stage
    sub_stage = Column(String(50), nullable=True)  # Maps to sub_stage
    date = Column(DateTime, nullable=True)  # Maps to date
    time = Column(String(50), nullable=True)  # Maps to time
    platform = Column(String(100), nullable=True)  # Maps to platform
    interview_questions = Column(JSON, nullable=True)  # Maps to interview_questions
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    application = relationship("JobApplication", back_populates="timeline")

# --- 20. Feedback ---
class Feedback(Base):
    __tablename__ = "feedback"
    feedback_id = Column(Integer, primary_key=True, autoincrement=True)
    application_id = Column(Integer, ForeignKey("job_applications.application_id", ondelete="CASCADE"))
    category = Column(String(50), nullable=True)  # Maps to feedback.category
    sub_category = Column(String(50), nullable=True)  # Maps to feedback.sub_category
    date = Column(DateTime, nullable=True)  # Maps to feedback.date
    comment = Column(Text, nullable=True)  # Maps to feedback.comment
    rating = Column(Integer, nullable=True)  # Maps to feedback.rating
    created_at = Column(DateTime, default=datetime.utcnow)
    candidate_id = Column(Integer, ForeignKey("candidates.candidate_id"), nullable=False)
    overall_score = Column(Integer, nullable=False)
    max_score = Column(Integer, default=100)
    general_comments = Column(Text, nullable=True)
    questions = Column(JSON, nullable=True)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    application = relationship("JobApplication")
    candidate = relationship("Candidate")

# --- 21. Company Details ---
class CompanyDetails(Base):
    __tablename__ = "company_details"
    company_id = Column(Integer, primary_key=True, autoincrement=True)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="CASCADE"), nullable=True)
    company_name = Column(String(255), nullable=True)  # Maps to company_name
    overview = Column(Text, nullable=True)  # Maps to overview
    ratings = Column(JSON, nullable=True)  # Maps to ratings
    diversity = Column(String(50), nullable=True)  # Maps to diversity
    financial_health = Column(String(50), nullable=True)  # Maps to financial_health
    sentiment = Column(String(50), nullable=True)  # Maps to sentiment
    benefits = Column(JSON, nullable=True)  # Maps to benefits
    recent_news = Column(JSON, nullable=True)  # Maps to recent_news
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    job = relationship("JobDescription", back_populates="company_details")

# --- 22. Profile Comparison ---
class ProfileComparison(Base):
    __tablename__ = "profile_comparison"
    comparison_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    experience_level = Column(JSON, nullable=True)  # Maps to profile_comparison.experience_level
    skill_diversity = Column(JSON, nullable=True)  # Maps high_matches to profile_comparison.skill_diversity
    education_level = Column(JSON, nullable=True)  # Maps to profile_comparison.education_level
    overall_competitiveness = Column(JSON, nullable=True)  # Maps to profile_comparison.overall_competitiveness
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User")

# --- 23. Recent Hires ---
class RecentHires(Base):
    __tablename__ = "recent_hires"
    hire_id = Column(Integer, primary_key=True, autoincrement=True)
    candidate_id = Column(Integer, ForeignKey("candidates.candidate_id", ondelete="CASCADE"))
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="CASCADE"))
    candidate_name = Column(String(255), nullable=True)  # Maps to recent_hires.candidate_name
    job_title = Column(String(255), nullable=True)  # Maps to recent_hires.job_title
    hire_date = Column(DateTime, nullable=True)  # Maps to recent_hires.hire_date
    initials = Column(String(10), nullable=True)  # Maps to recent_hires.initials
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    candidate = relationship("Candidate")
    job = relationship("JobDescription")

# --- 24. Today Interviews ---#availabilityslot-- --#interviewbookings
class TodayInterviews(Base):
    __tablename__ = "today_interviews"
    interview_id = Column(Integer, primary_key=True, autoincrement=True)
    candidate_id = Column(Integer, ForeignKey("candidates.candidate_id", ondelete="CASCADE"))
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="CASCADE"))
    candidate_name = Column(String(255), nullable=True)  # Maps to today_interviews.candidate_name
    job_title = Column(String(255), nullable=True)  # Maps to today_interviews.job_title
    time = Column(String(50), nullable=True)  # Maps to today_interviews.time
    status = Column(String(50), nullable=True)  # Maps to today_interviews.status
    slot_id = Column(Integer, nullable=True)  # Removed ForeignKey since AvailabilitySlot doesn't exist
    company_name = Column(String(255), nullable=False)
    job_position = Column(String(255), nullable=False)
    interview_date = Column(String(20), nullable=False)  # DD/MM/YYYY
    start_time = Column(String(10), nullable=False)      # HH:MM
    end_time = Column(String(10), nullable=False)        # HH:MM
    booking_status = Column(String(50), default="Pending")
    slot_type = Column(String(50), nullable=False, default="general")  # 'general' or 'job_specific'
    is_booked = Column(Boolean, default=False)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    job = relationship("JobDescription")
    candidate = relationship("Candidate")
    
 
# --- 25. Discussions ---
class Discussion(Base):
    __tablename__ = "discussions"
    discussion_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id"), nullable=True)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id"), nullable=True)
    title = Column(String(255), nullable=True)
    content = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User")
    job = relationship("JobDescription")

# --- 26. Recordings ---
class Recording(Base):
    __tablename__ = "recordings"
    recording_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id"), nullable=True)
    jd_id = Column(Integer, ForeignKey("job_descriptions.job_id"), nullable=True)
    recording_url = Column(String(255), nullable=True)
    transcript = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User")
    job = relationship("JobDescription")

# --- 27. Job Recruiter Assignments ---
class JobRecruiterAssignment(Base):
    __tablename__ = "job_recruiter_assignments"
    assignment_id = Column(Integer, primary_key=True, autoincrement=True)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id"))
    user_id = Column(Integer, ForeignKey("users.user_id"))
    assigned_at = Column(DateTime, default=datetime.utcnow)
    status = Column(String(50), default="active")
    
    # Relationships
    job = relationship("JobDescription")
    user = relationship("User")

# --- 28. Dashboard Content ---
class DashboardContent(Base):
    __tablename__ = "dashboard_contents"
    id = Column(Integer, primary_key=True, autoincrement=True)
    prompt = Column(Text, nullable=True)
    text = Column(Text, nullable=True)
    content = Column(Text, nullable=True)
    status = Column(String(50), default="active")
    created_at = Column(DateTime, default=datetime.utcnow)

# --- 29. Candidate Analytics ---
class CandidateAnalytics(Base):
    __tablename__ = "candidate_analytics"
    analytics_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"))
    profile_views = Column(Integer, nullable=True)  # Maps to profile_views
    change_profile_views = Column(Float, nullable=True)  # Maps to change_profile_views
    applications = Column(Integer, nullable=True)  # Maps to applications
    change_applications = Column(Float, nullable=True)  # Maps to change_applications
    interviews = Column(Integer, nullable=True)  # Maps to interviews
    change_interviews = Column(Float, nullable=True)  # Maps to change_interviews
    resume_uploads = Column(Integer, nullable=True)  # Maps to resume_uploads
    change_resume_uploads = Column(Float, nullable=True)  # Maps to change_resume_uploads
    job_searches = Column(Integer, nullable=True)  # Maps to job_searches
    change_job_searches = Column(Float, nullable=True)  # Maps to change_job_searches
    self_interviews = Column(Integer, nullable=True)  # Maps to self_interviews
    change_self_interviews = Column(Float, nullable=True)  # Maps to change_self_interviews
    average_response_time = Column(String(50), nullable=True)  # Maps to average_response_time
    conversion_rate = Column(Float, nullable=True)  # Maps to conversion_rate
    application_success_rate = Column(Float, nullable=True)  # Maps to application_success_rate
    interview_performance = Column(Float, nullable=True)  # Maps to interview_performance
    skills_improvement = Column(Float, nullable=True)  # Maps to skills_improvement
    learning_engagement = Column(Float, nullable=True)  # Maps to learning_engagement
    role_fitment = Column(Float, nullable=True)  # Maps to role_fitment
    learning_efficiency = Column(Float, nullable=True)  # Maps to learning_efficiency
    retention_rate = Column(Float, nullable=True)  # Maps to retention_rate
    change_retention_rate = Column(Float, nullable=True)  # Maps to change_retention_rate
    practice_score = Column(Float, nullable=True)  # Maps to practice_score
    change_practice_score = Column(Float, nullable=True)  # Maps to change_practice_score
    avg_rating = Column(Float, nullable=True)  # Maps to avg_rating
    completion_rate_analytics = Column(Float, nullable=True)  # Maps to completion_rate_analytics
    avg_match_score = Column(Float, nullable=True)  # Maps to avg_match_score
    high_matches = Column(Integer, nullable=True)  # Maps to high_matches
    total_revenue = Column(String(50), nullable=True)  # Maps to total_revenue
    monthly_growth = Column(Float, nullable=True)  # Maps to monthly_growth
    annual_growth = Column(Float, nullable=True)  # Maps to annual_growth
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="analytics")

    ### 30 --skill Gap Analysis ----
class SkillGapAnalysis(Base):
    __tablename__ = "skill_gap_analysis"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, ForeignKey("users.user_id", ondelete="CASCADE"), nullable=False)
    skill_id = Column(Integer, ForeignKey("skills.skill_id", ondelete="CASCADE"), nullable=False)
    job_id = Column(Integer, ForeignKey("job_descriptions.job_id", ondelete="CASCADE"), nullable=True)
    current_level = Column(Float, nullable=False)
    required_level = Column(Float, nullable=False)
    priority = Column(String(20), nullable=False)
    status = Column(String(30), nullable=False, default="Not Started")
    source = Column(String(50), nullable=True)
    gap_size = Column(Float, nullable=True)
    improvement_needed = Column(String(10), nullable=True, default="true")
    interview_score = Column(Integer, nullable=True)
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    skill = relationship("Skill", back_populates="skill_gap_analyses")
    job = relationship("JobDescription", back_populates="skill_gap_analyses")
    user = relationship("User", back_populates="skill_gap_analyses")


# --- Pydantic Models ---
class UserSkillModel(BaseModel):
    user_id: int
    skill_id: int
    proficiency_level: int
    years_of_experience: float
    
    class Config:
        from_attributes = True

class UserProfileModel(BaseModel):
    profile_id: int
    user_id: int
    job_title: str | None
    status: str | None
    profile_strength: float | None
    profile_completion: float | None
    work_experience_status: str | None
    portfolio_status: str | None
    basic_info_status: str | None
    resume_status: str | None
    skills_status: str | None
    location: str | None
    profile_score: float | None
    experience_level: float | None
    skill_count: int | None
    education_level: str | None
    overall_competitiveness: str | None
    expected_salary: str | None
    availability: str | None
    source: str | None
    
    class Config:
        from_attributes = True

class CandidateAnalyticsModel(BaseModel):
    analytics_id: int
    user_id: int
    profile_views: int | None
    change_profile_views: float | None
    applications: int | None
    change_applications: float | None
    interviews: int | None
    change_interviews: float | None
    resume_uploads: int | None
    change_resume_uploads: float | None
    job_searches: int | None
    change_job_searches: float | None
    self_interviews: int | None
    change_self_interviews: float | None
    average_response_time: str | None
    conversion_rate: float | None
    application_success_rate: float | None
    interview_performance: float | None
    skills_improvement: float | None
    learning_engagement: float | None
    role_fitment: float | None
    learning_efficiency: float | None
    retention_rate: float | None
    change_retention_rate: float | None
    practice_score: float | None
    change_practice_score: float | None
    avg_rating: float | None
    completion_rate_analytics: float | None
    avg_match_score: float | None
    high_matches: int | None
    total_revenue: str | None
    monthly_growth: float | None
    annual_growth: float | None
    
    class Config:
        from_attributes = True        
  
Base.metadata.create_all(bind=engine)
