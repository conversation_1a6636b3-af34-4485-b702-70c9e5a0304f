
# API Endpoints

@app.post("/generate-questions/")
async def generate_questions(resume: UploadFile = File(...), jd: UploadFile = File(...)):
    resume_content = await resume.read()
    jd_content = await jd.read()
    resume_text = extract_text(resume_content, resume.filename)
    jd_text = extract_text(jd_content, jd.filename)
    questions = await generate_questions_from_resume_and_jd(resume_text, jd_text)
    global latest_questions
    latest_questions = questions  # Store for later retrieval
    return {"questions": questions}

@app.get("/latest-questions/")
async def get_latest_questions():
    return {"questions": latest_questions}

def extract_text(file_bytes: bytes, filename: str) -> str:
    if filename.lower().endswith('.pdf'):
        with pdfplumber.open(io.BytesIO(file_bytes)) as pdf:
            return "\n".join(page.extract_text() or "" for page in pdf.pages)
    elif filename.lower().endswith('.docx'):
        doc = docx.Document(io.BytesIO(file_bytes))
        return "\n".join([para.text for para in doc.paragraphs])
    else:
        # fallback: try to decode as text
        return file_bytes.decode("utf-8", errors="ignore")

@app.post("/evaluate-answer/", response_model=BulkEvaluationResult)
async def evaluate_bulk_answers(request: BulkEvaluationRequest):
    evaluations = await evaluate_multiple_answers(request.answers)

    # Save to MongoDB after evaluation
    transcript_entry = {
        "transcript": request.answers,
        "evaluations": [eval.model_dump() for eval in evaluations],
    }
    transcripts_collection.insert_one(transcript_entry)

    return BulkEvaluationResult(evaluations=evaluations)

@app.post("/analyze-behavior/")
async def analyze_behavior_metrics(video_url: str):
    try:
        result = await analyze_behavior(video_url)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
@app.post("/save-transcript")
async def save_transcript(data: TranscriptSaveRequest):
    try:
        document = {
            "candidate_name": data.candidate_name,
            "questions": data.questions,
            "answers": data.answers,
            "transcript_text": data.transcript_text,
        }
        transcripts_collection.insert_one(document)
        return {"message": "Transcript saved successfully."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/upload")
async def upload_file(
    file: UploadFile = File(...),
    job_description: str = Form(...),
    experience_level: ExperienceLevel = Form(ExperienceLevel.BEGINNER),
    interview_date: Optional[str] = Form(None)
):
    """
    Upload a resume file and generate a learning path based on the job description.
    """
    try:
        logger.info(f"Received upload request for file: {file.filename}")
        
        # Validate file
        if not file or not file.filename:
            raise HTTPException(status_code=400, detail="No file uploaded")
            
        if not allowed_file(file.filename):
            raise HTTPException(status_code=400, detail="File type not allowed. Please upload a PDF or DOCX file")
        
        # Validate job description input
        if not job_description or len(job_description.strip()) < 50:
            raise HTTPException(
                status_code=400, 
                detail="Job description is required and must be at least 50 characters long"
            )
        
        # Extract text from the uploaded file
        logger.info("Extracting text from uploaded file...")
        resume_text = await extract_text_from_file(file)
        
        if not resume_text or len(resume_text.strip()) < 100:
            raise HTTPException(
                status_code=400, 
                detail="Could not extract meaningful text from the uploaded file. Please ensure the file contains readable text."
            )
        
        # Validate that the uploaded file contains resume content
        logger.info("Validating resume content...")
        resume_validation = validate_resume_content(resume_text)
        
        if not resume_validation.get('is_valid_resume', False):
            confidence = resume_validation.get('confidence_score', 0)
            missing_elements = resume_validation.get('missing_elements', [])
            validation_message = resume_validation.get('validation_message', 'Invalid resume format')
            
            error_detail = f"Resume validation failed: {validation_message}"
            if missing_elements:
                error_detail += f" Missing elements: {', '.join(missing_elements)}"
            
            raise HTTPException(
                status_code=400,
                detail=f"Invalid resume document. {error_detail}. Please upload a valid resume/CV that contains personal information, work experience, education, and skills."
            )
        
        # Validate job description content
        logger.info("Validating job description content...")
        job_validation = validate_job_description(job_description)
        
        if not job_validation.get('is_valid_job_description', False):
            confidence = job_validation.get('confidence_score', 0)
            missing_elements = job_validation.get('missing_elements', [])
            validation_message = job_validation.get('validation_message', 'Invalid job description format')
            
            error_detail = f"Job description validation failed: {validation_message}"
            if missing_elements:
                error_detail += f" Missing elements: {', '.join(missing_elements)}"
            
            raise HTTPException(
                status_code=400,
                detail=f"Invalid job description. {error_detail}. Please provide a complete job description with job title, responsibilities, requirements, and qualifications."
            )
        
        # Validate compatibility between resume and job description
        logger.info("Validating resume-job compatibility...")
        compatibility_result = validate_resume_job_compatibility(resume_validation, job_validation)
        
        if not compatibility_result.get('is_compatible', False):
            compatibility_score = compatibility_result.get('compatibility_score', 0)
            compatibility_reason = compatibility_result.get('compatibility_reason', 'Resume and job description are not compatible')
            role_match_level = compatibility_result.get('role_match_level', 'different')
            suggestions = compatibility_result.get('suggestions', [])
            
            resume_role = resume_validation.get('primary_role', 'Unknown')
            job_role = job_validation.get('job_role', 'Unknown')
            
            error_message = f"Resume-Job Mismatch: Your resume appears to be for a '{resume_role}' role, but the job description is for a '{job_role}' position. "
            error_message += f"Compatibility Score: {compatibility_score}%. "
            error_message += f"Reason: {compatibility_reason}"
            
            if suggestions:
                error_message += f" Suggestions: {'; '.join(suggestions)}"
            
            raise HTTPException(
                status_code=400,
                detail=error_message
            )
        
        logger.info(f"Resume and job description are compatible! Compatibility score: {compatibility_result.get('compatibility_score', 0)}%")
        
        # Calculate preparation hours and days until interview
        preparation_hours = None
        days_until_interview = None
        
        if interview_date:
            from datetime import datetime
            try:
                today = datetime.now().date()
                interview_dt = datetime.strptime(interview_date, "%Y-%m-%d").date()
                days_until_interview = (interview_dt - today).days
                if days_until_interview < 1:
                    days_until_interview = 1
                
                # Calculate available preparation hours (assuming 2-4 hours per day)
                preparation_hours = days_until_interview * 3  # 3 hours per day average
                
            except Exception as e:
                logger.warning(f"Could not parse interview_date: {interview_date}, error: {e}")
                days_until_interview = None
                preparation_hours = None
        
        logger.info("Generating learning path...")
        # Generate learning path
        learning_path = generate_learning_path(
            resume_text, 
            job_description, 
            preparation_hours=preparation_hours,
            days_until_interview=days_until_interview
        )
        
        if not isinstance(learning_path, dict):
            logger.error(f"Unexpected learning path format: {type(learning_path)}")
            learning_path = {}
        
        # Ensure all required fields are present with default values
        default_learning_path = {
            'chapters': [],
            'overallProgress': 0,
            'resumeMatchScore': compatibility_result.get('compatibility_score', 0),
            'strengths': [],
            'weakAreas': [],
            'timeEstimate': "8 hours",
            'interviewQuestions': [],
            'repeated_questions': [],
            'skills_analyzed': []
        }
        
        # Update with actual values from learning_path
        result = {**default_learning_path, **learning_path}
        
        # Add compatibility information to the result
        result['compatibility_info'] = {
            'resume_role': resume_validation.get('primary_role', 'Unknown'),
            'job_role': job_validation.get('job_role', 'Unknown'),
            'compatibility_score': compatibility_result.get('compatibility_score', 0),
            'role_match_level': compatibility_result.get('role_match_level', 'similar')
        }
        
        # Ensure chapters have required fields
        for chapter in result.get('chapters', []):
            if not isinstance(chapter, dict):
                continue
                
            chapter['completedModules'] = chapter.get('completedModules', 0)
            
            # Ensure modules exist and are properly formatted
            modules = chapter.get('modules', [])
            if not isinstance(modules, list):
                modules = []
                
            chapter['totalModules'] = len(modules)
            
            for module in modules:
                if not isinstance(module, dict):
                    continue
                module['completed'] = module.get('completed', False)

                # Normalize keyConcepts to always be objects with title and explanation
                content = module.get('content', {})
                key_concepts = content.get('keyConcepts', [])
                normalized = []
                for kc in key_concepts:
                    if isinstance(kc, str):
                        normalized.append({
                            'title': kc,
                            'explanation': 'Detailed explanation will be provided during study.'
                        })
                    elif isinstance(kc, dict) and 'title' in kc and 'explanation' in kc:
                        normalized.append(kc)
                content['keyConcepts'] = normalized
                module['content'] = content
        
        logger.info("Successfully generated learning path")
        return result
        
    except HTTPException as he:
        logger.error(f"HTTP Exception: {str(he)}")
        raise
    except Exception as e:
        error_msg = f"Error in upload_file: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.post("/api/modules/{module_id}/complete")
async def mark_module_complete(module_id: str):
    """Mark a module as completed."""
    return {"success": True}

@app.post("/api/company-questions")
async def get_company_questions(
    company: str = Body(...),
    role: str = Body(...),
    resume_text: str = Body(...),
    job_description: str = Body(...)
):
    """Generate company-specific interview questions and sample answers using Gemini."""
    try:
        # Validate resume content
        resume_validation = validate_resume_content(resume_text)
        if not resume_validation.get('is_valid_resume', False):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid resume content: {resume_validation.get('validation_message', 'Resume validation failed')}"
            )
        
        # Validate job description content
        job_validation = validate_job_description(job_description)
        if not job_validation.get('is_valid_job_description', False):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid job description: {job_validation.get('validation_message', 'Job description validation failed')}"
            )
        
        # Check compatibility
        compatibility_result = validate_resume_job_compatibility(resume_validation, job_validation)
        if not compatibility_result.get('is_compatible', False):
            resume_role = resume_validation.get('primary_role', 'Unknown')
            job_role = job_validation.get('job_role', 'Unknown')
            
            raise HTTPException(
                status_code=400,
                detail=f"Resume-Job Mismatch: Resume is for '{resume_role}' but job is for '{job_role}'. Compatibility score: {compatibility_result.get('compatibility_score', 0)}%"
            )
        
        prompt = f"""
        Generate exactly 4 realistic interview questions for a {role} position at {company}.
        
        RESUME SUMMARY:
        {resume_text}
        
        JOB DESCRIPTION:
        {job_description}
        
        Return ONLY a valid JSON array with this exact format:
        [
          {{
            "question": "Question text here",
            "difficulty": "easy",
            "category": "technical",
            "sampleAnswer": "Detailed answer here"
          }},
          {{
            "question": "Question text here", 
            "difficulty": "medium",
            "category": "behavioral",
            "sampleAnswer": "Detailed answer here"
          }}
        ]
        
        Requirements:
        - Return ONLY the JSON array, no other text
        - difficulty must be: easy, medium, or hard
        - category must be: technical, behavioral, or system-design
        - Each question should be relevant to the role and company
        - No markdown formatting, no comments
        """
        
        logger.info(f"Generating company questions for {company} - {role}")
        
        response = model.generate_content(prompt, generation_config={
            "max_output_tokens": 1500,
            "temperature": 0.7,
        })
        
        if not response.text:
            raise HTTPException(status_code=500, detail="No response from AI model")
        
        logger.info(f"Raw AI response: {response.text[:500]}...")
        
        # Clean and parse the response
        result = clean_json_response(response.text)
        
        # Validate the result
        if not isinstance(result, list):
            logger.error(f"AI returned non-list result: {type(result)} - {result}")
            # Create fallback questions
            result = [
                {
                    "question": f"Tell me about your experience with data science and how it relates to this {role} position at {company}.",
                    "difficulty": "easy",
                    "category": "behavioral",
                    "sampleAnswer": "I have 3 years of experience in data science, working with Python and machine learning frameworks. I've built predictive models and analyzed large datasets, which aligns well with Google's data-driven culture and the requirements for this role."
                },
                {
                    "question": "How would you approach building a machine learning model for a large-scale recommendation system?",
                    "difficulty": "medium", 
                    "category": "technical",
                    "sampleAnswer": "I would start by understanding the data structure and user behavior patterns, then choose appropriate algorithms like collaborative filtering or deep learning models, implement proper feature engineering, and ensure the system can scale using distributed computing frameworks."
                }
            ]
        
        # Ensure each question has required fields
        validated_questions = []
        for i, q in enumerate(result):
            if isinstance(q, dict):
                validated_q = {
                    "question": q.get("question", f"Sample question {i+1} for {role} at {company}"),
                    "difficulty": q.get("difficulty", "medium"),
                    "category": q.get("category", "technical"),
                    "sampleAnswer": q.get("sampleAnswer", "Sample answer would be provided here.")
                }
                validated_questions.append(validated_q)
        
        if not validated_questions:
            # Final fallback
            validated_questions = [
                {
                    "question": f"What interests you about working as a {role} at {company}?",
                    "difficulty": "easy",
                    "category": "behavioral", 
                    "sampleAnswer": f"I'm excited about {company}'s innovative approach to data science and the opportunity to work on large-scale problems that impact millions of users."
                }
            ]
        
        logger.info(f"Successfully generated {len(validated_questions)} questions")
        return {"questions": validated_questions}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_company_questions: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Error generating company questions: {str(e)}")
@app.post("/api/interview-resources")
async def get_interview_resources(
    role: str = Body(...),
    skills: list = Body(...),
    job_description: str = Body(...)
):
    """Generate a list of high-quality, up-to-date online resources for interview preparation."""
    try:
        # Validate job description content
        job_validation = validate_job_description(job_description)
        if not job_validation.get('is_valid_job_description', False):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid job description: {job_validation.get('validation_message', 'Job description validation failed')}"
            )
        
        prompt = f"""
        You are an expert career coach. Recommend 7-10 high-quality, up-to-date online resources (articles, tutorials, books, documentation, courses, repositories, etc.) for preparing for a {role} interview. The resources MUST be highly relevant to the following skills: {', '.join(skills)}. Use the job description for additional context and relevance. For each resource, provide:
        - title
        - url
        - type (e.g., Article, Book, Course, Documentation, Practice Platform, etc.)
        - category (e.g., Coding Interviews, Databases, System Design, etc.)
        Only include resources that are directly related to the skills and job description provided. Do NOT include generic resources. Return a JSON array of objects with these fields. Strictly valid JSON, no markdown, no comments, no trailing commas.
        JOB DESCRIPTION:
        {job_description}
        """
        response = model.generate_content(prompt, generation_config={
            "max_output_tokens": 1200,
            "temperature": 0.7,
        })
        result = clean_json_response(response.text)
        if not isinstance(result, list):
            raise HTTPException(status_code=500, detail="AI did not return a list of resources")
        return {"resources": result}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_interview_resources: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error generating interview resources: {str(e)}")

@app.get("/api/repeated-questions")
async def get_repeated_questions_endpoint(skills: str = None):
    """Get frequently asked questions for given skills."""
    try:
        if not skills:
            return {"repeated_questions": []}
        
        skills_list = [skill.strip() for skill in skills.split(',')]
        repeated_questions = get_repeated_questions(skills_list, limit=10)
        
        return {"repeated_questions": repeated_questions}
    
    except Exception as e:
        logger.error(f"Error in get_repeated_questions_endpoint: {str(e)}")
        return {"repeated_questions": []}

@app.get("/api/interview-stats")
async def get_interview_stats_endpoint():
    """Get comprehensive interview statistics."""
    try:
        stats = get_enhanced_interview_stats()
        return stats
    
    except Exception as e:
        logger.error(f"Error in get_interview_stats_endpoint: {str(e)}")
        return {
            "most_asked_questions": [],
            "category_distribution": [],
            "skills_frequency": []
        }

@app.post("/api/progress")
async def update_progress(
    chapter_id: str = Body(...),
    module_id: str = Body(...),
    completed: bool = Body(...)
):
    """Update progress for a specific module."""
    try:
        # In a real application, you would update this in a database
        # For now, we'll just return success
        logger.info(f"Progress updated: Chapter {chapter_id}, Module {module_id}, Completed: {completed}")
        return {"success": True, "message": "Progress updated successfully"}
    
    except Exception as e:
        logger.error(f"Error updating progress: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error updating progress: {str(e)}")

@app.get("/api/database-stats")
async def get_database_stats():
    """Get database statistics and health information."""
    try:
        conn = get_db_connection()
        if not conn:
            return {
                "status": "disconnected",
                "message": "Could not connect to database"
            }
        
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get table statistics
        cursor.execute("""
            SELECT 
                COUNT(*) as total_questions,
                COUNT(DISTINCT category) as unique_categories,
                COUNT(DISTINCT company) as unique_companies,
                AVG(asked_count) as avg_asked_count,
                MAX(asked_count) as max_asked_count
            FROM interview_questions
        """)
        
        stats = cursor.fetchone()
        
        # Get recent activity
        cursor.execute("""
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as questions_added
            FROM interview_questions 
            WHERE created_at >= CURRENT_DATE - INTERVAL '7 days'
            GROUP BY DATE(created_at)
            ORDER BY date DESC
        """)
        
        recent_activity = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        return {
            "status": "connected",
            "statistics": {
                "total_questions": stats['total_questions'] if stats else 0,
                "unique_categories": stats['unique_categories'] if stats else 0,
                "unique_companies": stats['unique_companies'] if stats else 0,
                "average_asked_count": float(stats['avg_asked_count']) if stats and stats['avg_asked_count'] else 0,
                "max_asked_count": stats['max_asked_count'] if stats else 0
            },
            "recent_activity": [
                {
                    "date": row['date'].isoformat() if row['date'] else None,
                    "questions_added": row['questions_added']
                } for row in recent_activity
            ]
        }
    
    except Exception as e:
        logger.error(f"Error getting database stats: {str(e)}")
        return {
            "status": "error",
            "message": str(e)
        }

@app.post("/api/seed-database")
async def seed_database():
    """Seed the database with sample interview questions for testing."""
    try:
        conn = get_db_connection()
        if not conn:
            raise HTTPException(status_code=500, detail="Could not connect to database")
        
        # Sample questions to seed the database
        sample_questions = [
            {
                "question": "Explain the difference between SQL and NoSQL databases",
                "skills": ["sql", "database", "nosql"],
                "difficulty": "medium",
                "category": "Database",
                "sample_answer": "SQL databases are relational and use structured query language, while NoSQL databases are non-relational and can handle unstructured data.",
                "hints": ["Think about structure", "Consider scalability", "ACID properties"]
            },
            {
                "question": "What is the difference between == and === in JavaScript?",
                "skills": ["javascript", "programming"],
                "difficulty": "easy",
                "category": "Programming",
                "sample_answer": "== performs type coercion while === checks for strict equality without type conversion.",
                "hints": ["Type coercion", "Strict comparison", "Best practices"]
            },
            {
                "question": "Explain the concept of microservices architecture",
                "skills": ["architecture", "microservices", "system design"],
                "difficulty": "hard",
                "category": "System Design",
                "sample_answer": "Microservices architecture breaks down applications into small, independent services that communicate over APIs.",
                "hints": ["Service independence", "Communication patterns", "Scalability benefits"]
            },
            {
                "question": "How do you handle state management in React?",
                "skills": ["react", "javascript", "frontend"],
                "difficulty": "medium",
                "category": "Frontend Development",
                "sample_answer": "React state can be managed using useState hook, useReducer, Context API, or external libraries like Redux.",
                "hints": ["Built-in hooks", "Context API", "External libraries"]
            },
            {
                "question": "What is the time complexity of binary search?",
                "skills": ["algorithms", "data structures", "computer science"],
                "difficulty": "easy",
                "category": "Algorithms",
                "sample_answer": "Binary search has O(log n) time complexity because it eliminates half of the search space in each iteration.",
                "hints": ["Divide and conquer", "Logarithmic growth", "Sorted array requirement"]
            }
        ]
        
        cursor = conn.cursor()
        seeded_count = 0
        
        for question_data in sample_questions:
            try:
                question_hash = hashlib.sha256(question_data["question"].encode()).hexdigest()
                
                # Check if question already exists
                cursor.execute("SELECT id FROM interview_questions WHERE question_hash = %s", (question_hash,))
                if cursor.fetchone():
                    continue  # Skip if already exists
                
                cursor.execute('''
                    INSERT INTO interview_questions 
                    (question, question_hash, skills, difficulty, category, sample_answer, hints, asked_count)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ''', (
                    question_data["question"],
                    question_hash,
                    question_data["skills"],
                    question_data["difficulty"],
                    question_data["category"],
                    question_data["sample_answer"],
                    question_data["hints"],
                    1
                ))
                seeded_count += 1
                
            except Exception as e:
                logger.warning(f"Could not seed question: {question_data['question'][:50]}... Error: {str(e)}")
                continue
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return {
            "success": True,
            "message": f"Successfully seeded {seeded_count} questions into the database",
            "seeded_count": seeded_count
        }
    
    except Exception as e:
        logger.error(f"Error seeding database: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error seeding database: {str(e)}")

