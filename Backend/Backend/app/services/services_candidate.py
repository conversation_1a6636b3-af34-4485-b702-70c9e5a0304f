import io
import os
import json
import time
import traceback
import uuid
import re
import pdfplumber
import docx
import docx2txt
import PyPDF2
from fastapi import HTTPException, UploadFile
from tempfile import NamedTemporaryFile
from typing import Optional

from config import model, logger, ALLOWED_EXTENSIONS
import database

def allowed_file(filename: str) -> bool:
    """Check if the file has an allowed extension."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def extract_text(file_bytes: bytes, filename: str) -> str:
    """Extracts text from pdf, docx, or plain text bytes."""
    if filename.lower().endswith('.pdf'):
        with pdfplumber.open(io.BytesIO(file_bytes)) as pdf:
            return "\n".join(page.extract_text() or "" for page in pdf.pages)
    elif filename.lower().endswith('.docx'):
        doc = docx.Document(io.BytesIO(file_bytes))
        return "\n".join([para.text for para in doc.paragraphs])
    else:
        return file_bytes.decode("utf-8", errors="ignore")

def extract_text_from_pdf(file_path: str) -> str:
    """Extract text from PDF file."""
    try:
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text() + "\n"
        return text
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Error extracting text from PDF: {str(e)}")

async def extract_text_from_file(file: UploadFile) -> str:
    """Extract text from uploaded file (PDF or DOCX)."""
    try:
        file_extension = file.filename.split('.')[-1].lower()
        with NamedTemporaryFile(delete=False, suffix=f".{file_extension}") as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        try:
            if file_extension == 'pdf':
                return extract_text_from_pdf(temp_file_path)
            else:  # docx
                return docx2txt.process(temp_file_path)
        finally:
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
    except Exception as e:
        logger.error(f"Error extracting text from file: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Error processing file: {str(e)}")

def clean_json_response(response_text: str) -> dict:
    """Clean and parse JSON response from AI."""
    import re
    import json

    if not response_text or not response_text.strip():
        logger.warning("Empty response text provided to clean_json_response")
        return {}

    response_text = response_text.strip()

    # Remove markdown code block markers if present
    if response_text.startswith("```json"):
        response_text = response_text[len("```json"):].strip()
    elif response_text.startswith("```"):
        response_text = response_text[len("```"):].strip()
    
    if response_text.endswith("```"):
        response_text = response_text[:-3].strip()

    # Remove trailing commas before } or ]
    response_text = re.sub(r',(\s*[}\]])', r'\1', response_text)
    
    # Fix common JSON issues
    response_text = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', response_text)  # Quote unquoted keys
    response_text = re.sub(r':\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*([,}])', r': "\1"\2', response_text)  # Quote unquoted string values

    # Log the cleaned response for debugging
    logger.debug(f"Cleaned AI response for JSON parsing (first 500 chars): {response_text[:500]}...")

    try:
        parsed = json.loads(response_text)
        logger.info("Successfully parsed JSON response")
        return parsed
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON: {e}")
        logger.error(f"Problematic JSON (first 1000 chars): {response_text[:1000]}")
        
        # Try to extract JSON from the response if it's embedded in text
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            try:
                extracted_json = json_match.group(0)
                logger.info("Attempting to parse extracted JSON")
                return json.loads(extracted_json)
            except json.JSONDecodeError:
                logger.error("Failed to parse extracted JSON as well")
        
        return {}


    
def validate_resume_content(text: str) -> dict:
    """Validate if the extracted text contains resume-like content using AI."""
    try:
        logger.info("Validating resume content using AI...")
        
        validation_prompt = f"""
        Analyze the following text and determine if it's a valid resume/CV document. 
        
        TEXT TO ANALYZE:
        {text[:3000]}
        
        Check for the presence of these resume elements:
        1. Personal/Contact information (name, email, phone, etc.)
        2. Work experience or employment history
        3. Education background
        4. Skills section
        5. Professional summary or objective
        
        Also identify the primary career field/role from the resume.
        
        Return a JSON response with this exact structure:
        {{
            "is_valid_resume": true/false,
            "confidence_score": 0-100,
            "primary_role": "Main career field/role identified from resume",
            "career_keywords": ["keyword1", "keyword2", "keyword3"],
            "found_elements": ["element1", "element2", ...],
            "missing_elements": ["element1", "element2", ...],
            "validation_message": "Detailed explanation of why this is or isn't a resume"
        }}
        """
        
        response = model.generate_content(validation_prompt, generation_config={
            "max_output_tokens": 600,
            "temperature": 0.3,
        })
        
        if not response.text:
            raise ValueError("No validation response from AI")
        
        validation_result = clean_json_response(response.text)
        logger.info(f"Resume validation result: {validation_result}")
        
        return validation_result
        
    except Exception as e:
        logger.error(f"Error validating resume content: {str(e)}")
        return {
            "is_valid_resume": False,
            "confidence_score": 0,
            "primary_role": "Unknown",
            "career_keywords": [],
            "found_elements": [],
            "missing_elements": ["Unable to validate"],
            "validation_message": f"Error during validation: {str(e)}"
        }


def validate_job_description(text: str) -> dict:
    """Validate if the provided text contains a proper job description."""
    try:
        logger.info("Validating job description content using AI...")
        
        validation_prompt = f"""
        Analyze the following text and determine if it's a valid job description for a specific role.
        
        TEXT TO ANALYZE:
        {text[:2000]}
        
        STRICT VALIDATION CRITERIA:
        1. Must contain a clear job title or position name
        2. Must specify company information or hiring context
        3. Must list specific job responsibilities or duties (at least 3-4)
        4. Must include required skills, qualifications, or experience
        5. Must be for a legitimate professional role
        6. Must be more than just a generic description
        
        REJECT if the text is:
        - Too generic or vague
        - Just a list of random skills
        - Not a real job posting
        - Missing key job description elements
        - Clearly not a professional job description
        
        Be STRICT in your validation. Only return true for legitimate, detailed job descriptions.
        
        Return a JSON response with this exact structure:
        {{
            "is_valid_job_description": true/false,
            "confidence_score": 0-100,
            "job_role": "Main job role/field identified from job description",
            "job_keywords": ["keyword1", "keyword2", "keyword3"],
            "found_elements": ["element1", "element2", ...],
            "missing_elements": ["element1", "element2", ...],
            "validation_message": "Detailed explanation of why this is or isn't a job description"
        }}
        """
        
        response = model.generate_content(validation_prompt, generation_config={
            "max_output_tokens": 600,
            "temperature": 0.1,  # Lower temperature for more consistent validation
        })
        
        if not response.text:
            raise ValueError("No validation response from AI")
        
        validation_result = clean_json_response(response.text)
        logger.info(f"Job description validation result: {validation_result}")
        
        # Additional strict checks
        if validation_result.get('is_valid_job_description', False):
            confidence = validation_result.get('confidence_score', 0)
            if confidence < 70:  # Require high confidence
                validation_result['is_valid_job_description'] = False
                validation_result['validation_message'] = f"Low confidence score ({confidence}%). Job description may be too generic or incomplete."
        
        return validation_result
        
    except Exception as e:
        logger.error(f"Error validating job description: {str(e)}")
        return {
            "is_valid_job_description": False,
            "confidence_score": 0,
            "job_role": "Unknown",
            "job_keywords": [],
            "found_elements": [],
            "missing_elements": ["Unable to validate"],
            "validation_message": f"Error during validation: {str(e)}"
        }

def validate_resume_job_compatibility(resume_validation: dict, job_validation: dict) -> dict:
    """Validate if the resume and job description are compatible with each other."""
    try:
        logger.info("Validating resume-job compatibility using AI...")
        
        resume_role = resume_validation.get('primary_role', 'Unknown')
        job_role = job_validation.get('job_role', 'Unknown')
        resume_keywords = resume_validation.get('career_keywords', [])
        job_keywords = job_validation.get('job_keywords', [])
        
        compatibility_prompt = f"""
        Analyze the compatibility between a resume and job description. Be STRICT in your assessment.
        
        RESUME ANALYSIS:
        - Primary Role: {resume_role}
        - Career Keywords: {resume_keywords}
        
        JOB DESCRIPTION ANALYSIS:
        - Job Role: {job_role}
        - Job Keywords: {job_keywords}
        
        STRICT COMPATIBILITY RULES:
        1. Roles must be in the same field or closely related
        2. There must be significant skill overlap (at least 30%)
        3. Experience level should be appropriate
        4. Career progression should make sense
        
        EXAMPLES OF INCOMPATIBLE COMBINATIONS:
        - Data Scientist resume + Marketing Manager job
        - Frontend Developer resume + Data Scientist job
        - Accountant resume + Software Engineer job
        - Student resume + Senior Executive job
        
        Be STRICT. Only mark as compatible if there's a genuine match.
        
        Return a JSON response with this exact structure:
        {{
            "is_compatible": true/false,
            "compatibility_score": 0-100,
            "compatibility_reason": "Detailed explanation of why they are or aren't compatible",
            "role_match_level": "exact/similar/different/unrelated",
            "skill_overlap_percentage": 0-100,
            "suggestions": ["suggestion1", "suggestion2"]
        }}
        """
        
        response = model.generate_content(compatibility_prompt, generation_config={
            "max_output_tokens": 500,
            "temperature": 0.1,  # Lower temperature for more consistent validation
        })
        
        if not response.text:
            raise ValueError("No compatibility response from AI")
        
        compatibility_result = clean_json_response(response.text)
        logger.info(f"Compatibility validation result: {compatibility_result}")
        
        # Additional strict checks
        if compatibility_result.get('is_compatible', False):
            compatibility_score = compatibility_result.get('compatibility_score', 0)
            skill_overlap = compatibility_result.get('skill_overlap_percentage', 0)
            role_match = compatibility_result.get('role_match_level', 'different')
            
            # Strict compatibility requirements
            if compatibility_score < 60 or skill_overlap < 25 or role_match in ['different', 'unrelated']:
                compatibility_result['is_compatible'] = False
                compatibility_result['compatibility_reason'] = f"Insufficient compatibility: Score {compatibility_score}%, Skill overlap {skill_overlap}%, Role match: {role_match}"
        
        return compatibility_result
        
    except Exception as e:
        logger.error(f"Error validating compatibility: {str(e)}")
        return {
            "is_compatible": False,
            "compatibility_score": 0,
            "compatibility_reason": f"Error during compatibility check: {str(e)}",
            "role_match_level": "unknown",
            "skill_overlap_percentage": 0,
            "suggestions": ["Please try again with valid documents"]
        }

def generate_learning_path(resume_text: str, job_description: str, preparation_hours: int = None, days_until_interview: Optional[int] = None) -> dict:
    """Generate a learning path using Gemini based on resume, job description, and preparation time."""
    try:
        logger.info("Starting learning path generation...")
        
        # Log input data (truncated for readability)
        logger.debug(f"Resume text (first 500 chars): {resume_text[:500]}...")
        logger.debug(f"Job Description (first 500 chars): {job_description[:500]}...")
        if preparation_hours:
            logger.debug(f"Preparation hours: {preparation_hours}")
        if days_until_interview is not None:
            logger.debug(f"Days until interview: {days_until_interview}")
        
        time_constraint = ""
        if preparation_hours:
            time_constraint = f"\nIMPORTANT: The user has only {preparation_hours} hours to prepare. The total estimated time for the learning plan should fit within this period. Adjust the number of chapters, modules, and time estimates accordingly. Prioritize the most important topics for the job."
        elif days_until_interview is not None:
            time_constraint = f"\nIMPORTANT: The user has {days_until_interview} days to prepare. Adjust the learning plan accordingly."
        
        prompt = f"""
        You are an expert career coach and technical interviewer. Analyze the following resume and job description to create a highly personalized interview preparation plan.
        
        RESUME (First 10,000 characters):
        {resume_text[:10000]}
        
        TARGET JOB DESCRIPTION:
        {job_description}
        
        Create a comprehensive, structured learning path with the following sections:
        
        1. TECHNICAL SKILLS ASSESSMENT:
           - Identify 3-4 key technical strengths from the resume that match the job requirements
           - Identify 4-5 technical gaps that need improvement
        
        2. CORE CONCEPT MODULES:
           For each major technical area required by the job:
           - Module title (specific to the technology/concept)
           - Key learning objectives (7-9 bullet points)
           - Estimated study time (1-4 hours per module)
           - Recommended resources (specific articles, documentation, tutorials)
           - Practice exercises (3-4 per module)
        
        Return the response as a JSON object with this EXACT structure:
        {{
          "chapters": [
            {{
              "id": "unique-id",
              "title": "Chapter Title",
              "modules": [
                {{
                  "id": "unique-module-id",
                  "title": "Module Title",
                  "completed": false,
                  "duration": "X hours",
                  "difficulty": experience_level.capitalize(),  # <-- Set based on user selection
                  "content": {{
                    "summary": "Brief overview of what this module covers",
                    "keyConcepts": [
                      {{"title": "Concept 1", "explanation": "Detailed explanation of concept 1 in 9-10 sentences."}},
                      {{"title": "Concept 2", "explanation": "Detailed explanation of concept 2 in 9-10 sentences."}}
                    ],
                    "codeExamples": [
                      {{
                        "language": "Any Language based on resume and skills",
                        "code": "Code snippet related to the topic",
                        "explanation": "Explanation of the above snippet"
                      }}
                    ],
                    "personalizedTips": ["Tip 1", "Tip 2"]
                  }}
                }}
              ],
              "completedModules": 0,
              "totalModules": 3
            }}
          ],
          "overallProgress": 0,
          "resumeMatchScore": 75,
          "strengths": ["strength1", "strength2"],
          "weakAreas": ["area1", "area2"],
          "timeEstimate": "20 hours",
          "skills_analyzed": ["skill1", "skill2", "skill3"],
          "interviewQuestions": [
            {{
              "id": "q1",
              "question": "Question text",
              "difficulty": "easy",
              "category": "technical",
              "hints": ["hint1", "hint2"],
              "sampleAnswer": "Structured answer"
            }}
          ]
        }}
        
        IMPORTANT REQUIREMENTS:
        - Limit the response to 2-3 chapters, each with 2-3 modules.
        - The response must be STRICTLY valid JSON (no comments, no trailing commas, all property names in double quotes, no markdown, no truncation).
        - Each module must have a unique ID and title
        - Include code examples in the appropriate programming language
        - Difficulty must be one of: Beginner, Intermediate, or Advanced
        - Duration should be in format like "30 min" or "2 hours"
        - For each keyConcept, provide both a 'title' and a detailed 'explanation'
        - Make sure ALL required fields are present in the response
        - The response should be complete and ready to use in the frontend
        - Do not truncate or abbreviate the response
        {time_constraint}
        """
        
        logger.info("Sending request to Gemini API...")
        start_time = time.time()
        
        try:
            response = model.generate_content(prompt, generation_config={
                "max_output_tokens": 4000,
                "temperature": 0.7,
            })
            
            process_time = time.time() - start_time
            logger.info(f"Received response from Gemini API in {process_time:.2f} seconds")
            
            if not response.text:
                error_msg = "No content generated by the model"
                logger.error(error_msg)
                raise ValueError(error_msg)
            
            # Log the raw response (first 1000 chars to avoid log flooding)
            logger.debug(f"Raw AI response (first 1000 chars): {response.text[:1000]}...")
            
            # Process the response
            result = clean_json_response(response.text)
            
            # Create default structure if parsing failed
            if not result or not isinstance(result, dict):
                logger.warning("Failed to parse AI response, creating default structure")
                result = {}
            
            # Ensure all required fields are present with default values
            default_learning_path = {
                'chapters': [],
                'overallProgress': 0,
                'resumeMatchScore': 75,
                'strengths': ["Technical experience", "Problem-solving skills"],
                'weakAreas': ["Interview preparation", "Technical communication"],
                'timeEstimate': "8 hours",
                'interviewQuestions': [],
                'skills_analyzed': ["General technical skills"]
            }
            
            # Merge with actual values from result
            for key, default_value in default_learning_path.items():
                if key not in result:
                    result[key] = default_value
                    logger.info(f"Added missing field '{key}' with default value")
            
            # Validate and fix chapters structure
            if not isinstance(result.get('chapters'), list):
                result['chapters'] = []
                logger.warning("Fixed invalid chapters structure")
            
            # If no chapters were generated, create a basic one
            if not result['chapters']:
                logger.info("No chapters found, creating default chapter")
                result['chapters'] = [{
                    'id': f"chapter-{uuid.uuid4().hex[:8]}",
                    'title': "Interview Preparation Fundamentals",
                    'modules': [{
                        'id': f"module-{uuid.uuid4().hex[:8]}",
                        'title': "Getting Started",
                        'completed': False,
                        'duration': "1 hour",
                        'difficulty': "Beginner",
                        'content': {
                            'summary': "Introduction to interview preparation based on your background",
                            'keyConcepts': [
                                {
                                    'title': "Interview Preparation Strategy",
                                    'explanation': "A systematic approach to preparing for technical interviews involves understanding the job requirements, assessing your current skills, and creating a focused study plan."
                                }
                            ],
                            'codeExamples': [{
                                'language': 'python',
                                'code': '# Example: Basic problem-solving approach\ndef solve_problem(input_data):\n    # Understand the problem\n    # Plan your solution\n    # Implement step by step\n    # Test your solution\n    return result',
                                'explanation': 'This template shows a structured approach to solving coding problems during interviews.'
                            }],
                            'personalizedTips': [
                                "Practice explaining your thought process out loud",
                                "Review the job description thoroughly before the interview"
                            ]
                        }
                    }],
                    'completedModules': 0,
                    'totalModules': 1
                }]
            
            # Ensure each chapter has required fields
            for chapter in result['chapters']:
                if not isinstance(chapter, dict):
                    continue
                    
                if 'id' not in chapter:
                    chapter['id'] = f"chapter-{uuid.uuid4().hex[:8]}"
                if 'title' not in chapter:
                    chapter['title'] = "Untitled Chapter"
                if 'modules' not in chapter or not isinstance(chapter['modules'], list):
                    chapter['modules'] = []
                if 'completedModules' not in chapter:
                    chapter['completedModules'] = 0
                if 'totalModules' not in chapter:
                    chapter['totalModules'] = len(chapter.get('modules', []))
                
                # Ensure each module has required fields
                for module in chapter['modules']:
                    if not isinstance(module, dict):
                        continue
                        
                    if 'id' not in module:
                        module['id'] = f"module-{uuid.uuid4().hex[:8]}"
                    if 'title' not in module:
                        module['title'] = "Untitled Module"
                    if 'completed' not in module:
                        module['completed'] = False
                    if 'duration' not in module:
                        module['duration'] = "30 min"
                    if 'difficulty' not in module:
                        module['difficulty'] = "Beginner"
                    if 'content' not in module or not isinstance(module['content'], dict):
                        module['content'] = {
                            'summary': 'Module content summary',
                            'keyConcepts': [],
                            'codeExamples': [],
                            'personalizedTips': []
                        }
                    
                    # Ensure content has required fields
                    content = module['content']
                    if 'summary' not in content:
                        content['summary'] = 'Module content summary'
                    if 'keyConcepts' not in content:
                        content['keyConcepts'] = []
                    if 'codeExamples' not in content:
                        content['codeExamples'] = []
                    if 'personalizedTips' not in content:
                        content['personalizedTips'] = []
                    
                    # Normalize keyConcepts to always be objects with title and explanation
                    key_concepts = content.get('keyConcepts', [])
                    normalized = []
                    for kc in key_concepts:
                        if isinstance(kc, str):
                            normalized.append({
                                'title': kc,
                                'explanation': 'Detailed explanation will be provided during study.'
                            })
                        elif isinstance(kc, dict) and 'title' in kc:
                            if 'explanation' not in kc:
                                kc['explanation'] = 'Detailed explanation will be provided during study.'
                            normalized.append(kc)
                    content['keyConcepts'] = normalized
            
            # Add time analysis if preparation hours provided
            if preparation_hours:
                result['time_analysis'] = {
                    'available_hours': preparation_hours,
                    'priority_level': 'High' if preparation_hours <= 8 else 'Medium' if preparation_hours <= 24 else 'Standard',
                    'recommended_schedule': f"Focus on {len(result['chapters'])} key areas over {preparation_hours} hours"
                }
            
            # Store interview questions in database
            try:
                skills = result.get('skills_analyzed', [])
                for question in result.get('interviewQuestions', []):
                    if isinstance(question, dict) and question.get('question'):
                        store_interview_question(
                            question.get('question', ''),
                            skills,
                            question.get('difficulty', 'medium'),
                            question.get('category', 'technical'),
                            job_description=job_description,
                            sample_answer=question.get('sampleAnswer'),
                            hints=question.get('hints', [])
                        )
            except Exception as e:
                logger.warning(f"Could not store questions in database: {str(e)}")
            
            # Get repeated questions for the skills
            try:
                skills = result.get('skills_analyzed', [])
                if skills:
                    repeated_questions = get_repeated_questions_enhanced(skills, job_description, limit=5)
                    result['repeated_questions'] = repeated_questions
                else:
                    result['repeated_questions'] = []
            except Exception as e:
                logger.warning(f"Could not get repeated questions: {str(e)}")
                result['repeated_questions'] = []
            
            logger.info(f"Successfully generated learning path with {len(result['chapters'])} chapters")
            
            return result
            
        except Exception as e:
            process_time = time.time() - start_time
            logger.error(f"Error during Gemini API call (took {process_time:.2f}s): {str(e)}")
            logger.error(f"Error type: {type(e).__name__}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise
        
    except Exception as e:
        logger.error(f"Error in generate_learning_path: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Return a fallback learning path instead of raising an exception
        logger.info("Returning fallback learning path due to error")
        return {
            'chapters': [{
                'id': f"fallback-chapter-{uuid.uuid4().hex[:8]}",
                'title': "Interview Preparation Essentials",
                'modules': [{
                    'id': f"fallback-module-{uuid.uuid4().hex[:8]}",
                    'title': "Getting Started with Interview Prep",
                    'completed': False,
                    'duration': "1 hour",
                    'difficulty': "Beginner",
                    'content': {
                        'summary': "Essential interview preparation strategies and techniques",
                        'keyConcepts': [
                            {
                                'title': "Interview Preparation Framework",
                                'explanation': "A structured approach to preparing for technical interviews includes understanding the role requirements, practicing problem-solving, and preparing for behavioral questions."
                            }
                        ],
                        'codeExamples': [{
                            'language': 'python',
                            'code': '# Basic problem-solving template\ndef interview_problem_approach(problem):\n    # 1. Understand the problem\n    # 2. Ask clarifying questions\n    # 3. Think through examples\n    # 4. Design solution\n    # 5. Code and test\n    return solution',
                            'explanation': 'This template provides a systematic approach to tackling coding interview problems.'
                        }],
                                                'personalizedTips': [
                            "Practice coding problems daily",
                            "Review fundamental computer science concepts",
                            "Prepare stories for behavioral questions using the STAR method",
                            "Research the company and role thoroughly"
                        ]
                    }
                }],
                'completedModules': 0,
                'totalModules': 1
            }],
            'overallProgress': 0,
            'resumeMatchScore': 50,
            'strengths': ["Problem-solving ability", "Technical background"],
            'weakAreas': ["Interview-specific preparation", "Technical communication"],
            'timeEstimate': "8 hours",
            'interviewQuestions': [{
                'id': f"fallback-q-{uuid.uuid4().hex[:8]}",
                'question': "Tell me about yourself and your technical background.",
                'difficulty': "easy",
                'category': "behavioral",
                'hints': ["Focus on relevant experience", "Keep it concise and structured"],
                'sampleAnswer': "I am a [your role] with [X years] of experience in [relevant technologies]. In my previous role at [company], I worked on [specific projects] where I [achievements]. I'm particularly interested in this position because [reason related to the job]."
            }],
            'skills_analyzed': ["General technical skills", "Problem solving"],
            'repeated_questions': [],
            'error_occurred': True,
            'error_message': f"AI generation failed: {str(e)}"
        }
    
def init_database():
    """Initialize PostgreSQL database for storing interview questions."""
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                # Schema definition is intentionally here, as it's foundational
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS interview_questions (
                        id SERIAL PRIMARY KEY, question TEXT NOT NULL, question_hash VARCHAR(64) UNIQUE NOT NULL,
                        skills TEXT[] NOT NULL, difficulty VARCHAR(20) NOT NULL, category VARCHAR(50) NOT NULL,
                        company VARCHAR(100), role VARCHAR(100), job_description_keywords TEXT[],
                        asked_count INTEGER DEFAULT 1, last_asked TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, sample_answer TEXT, hints TEXT[]
                    )
                ''')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_interview_questions_skills ON interview_questions USING GIN (skills)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_interview_questions_keywords ON interview_questions USING GIN (job_description_keywords)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_interview_questions_category ON interview_questions (category)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_interview_questions_hash ON interview_questions (question_hash)')
                conn.commit()
        logger.info("PostgreSQL database initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Error initializing PostgreSQL database: {e}")
        return False

def store_interview_question(question: str, skills: list, difficulty: str, category: str, job_keywords: list,
                           company: str = None, role: str = None, sample_answer: str = None, hints: list = None):
    """Store or update an interview question in the PostgreSQL database."""
    question_hash = hashlib.sha256(question.encode()).hexdigest()
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute('SELECT id FROM interview_questions WHERE question_hash = %s', (question_hash,))
                existing = cursor.fetchone()

                if existing:
                    cursor.execute('''
                        UPDATE interview_questions SET asked_count = asked_count + 1, last_asked = CURRENT_TIMESTAMP,
                        company = COALESCE(%s, company), role = COALESCE(%s, role),
                        job_description_keywords = COALESCE(%s, job_description_keywords),
                        sample_answer = COALESCE(%s, sample_answer), hints = COALESCE(%s, hints)
                        WHERE id = %s
                    ''', (company, role, job_keywords, sample_answer, hints or [], existing[0]))
                else:
                    cursor.execute('''
                        INSERT INTO interview_questions (question, question_hash, skills, difficulty, category, company, role, job_description_keywords, sample_answer, hints)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ''', (question, question_hash, skills or [], difficulty, category, company, role, job_keywords, sample_answer, hints or []))
                conn.commit()
    except Exception as e:
        logger.error(f"Error storing interview question: {e}")

def get_repeated_questions_enhanced(skills: list, job_keywords: list) -> list:
    """Get frequently asked questions using configurable limits and weights."""
    if not skills and not job_keywords:
        return []
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                query = """
                    SELECT q.*,
                           (CASE WHEN skills && %s THEN %s ELSE 0 END +
                            CASE WHEN job_description_keywords && %s THEN %s ELSE 0 END) as relevance_score
                    FROM interview_questions q
                    WHERE q.asked_count > %s AND (q.skills && %s OR q.job_description_keywords && %s)
                    ORDER BY relevance_score DESC, q.asked_count DESC, q.last_asked DESC
                    LIMIT %s
                """
                params = (
                    skills, settings.RELEVANCE_SCORE_SKILL_MATCH,
                    job_keywords, settings.RELEVANCE_SCORE_KEYWORD_MATCH,
                    settings.REPEATED_QUESTION_THRESHOLD,
                    skills, job_keywords,
                    settings.REPEATED_QUESTIONS_LIMIT
                )
                cursor.execute(query, params)
                results = cursor.fetchall()
                logger.info(f"Retrieved {len(results)} enhanced repeated questions.")
                return [dict(row) for row in results]
    except Exception as e:
        logger.error(f"Error getting enhanced repeated questions: {e}")
        return []

def get_enhanced_interview_stats() -> dict:
    """Get comprehensive interview statistics using configurable limits."""
    stats = {"most_asked_questions": [], "category_distribution": [], "skills_frequency": []}
    try:
        with get_db_connection() as conn:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                cursor.execute('SELECT * FROM interview_questions WHERE asked_count > %s ORDER BY asked_count DESC LIMIT %s', 
                               (settings.REPEATED_QUESTION_THRESHOLD, settings.STATS_LIMIT_MOST_ASKED))
                stats["most_asked_questions"] = [dict(row) for row in cursor.fetchall()]

                cursor.execute('SELECT category, COUNT(*) as count FROM interview_questions GROUP BY category ORDER BY count DESC')
                stats["category_distribution"] = [dict(row) for row in cursor.fetchall()]

                cursor.execute('SELECT unnest(skills) as skill, COUNT(*) as count FROM interview_questions GROUP BY skill ORDER BY count DESC LIMIT %s',
                               (settings.STATS_LIMIT_SKILLS_FREQ,))
                stats["skills_frequency"] = [dict(row) for row in cursor.fetchall()]
        return stats
    except Exception as e:
        logger.error(f"Error getting enhanced interview stats: {e}")
        return stats

def seed_database_task():
    """Seeds the database by dynamically loading questions from a JSON file."""
    try:
        with open(settings.SEED_DATA_FILE, 'r') as f:
            sample_questions = json.load(f)
    except FileNotFoundError:
        logger.error(f"Seed data file not found: {settings.SEED_DATA_FILE}")
        return 0
    except json.JSONDecodeError:
        logger.error(f"Could not parse JSON from seed data file: {settings.SEED_DATA_FILE}")
        return 0

    seeded_count = 0
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                for q_data in sample_questions:
                    q_hash = hashlib.sha256(q_data["question"].encode()).hexdigest()
                    cursor.execute("SELECT id FROM interview_questions WHERE question_hash = %s", (q_hash,))
                    if cursor.fetchone():
                        continue
                    cursor.execute(
                        'INSERT INTO interview_questions (question, question_hash, skills, difficulty, category, sample_answer, hints, asked_count) VALUES (%s, %s, %s, %s, %s, %s, %s, 1)',
                        (q_data["question"], q_hash, q_data.get("skills", []), q_data["difficulty"], q_data["category"], q_data.get("sample_answer"), q_data.get("hints", []))
                    )
                    seeded_count += 1
                conn.commit()
        if seeded_count > 0:
            logger.info(f"Successfully seeded {seeded_count} new questions from {settings.SEED_DATA_FILE}.")
        else:
            logger.info("Database is already seeded with the questions from the seed file.")
        return seeded_count
    except Exception as e:
        logger.error(f"Error seeding database: {e}")
        return 0