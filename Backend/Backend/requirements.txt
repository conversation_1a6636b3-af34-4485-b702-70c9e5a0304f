# Core FastAPI and ASGI server
fastapi>=0.68.0
uvicorn>=0.15.0
supabase==2.3.4
httpx==0.25.2
python-dateutil==2.8.2
alembic==1.13.1
# Database
sqlalchemy>=1.4.0
psycopg2-binary>=2.9.1
aiosqlite>=0.19.0  # For SQLite async support

# File handling and parsing
python-multipart>=0.0.5
PyPDF2>=3.0.0
python-docx>=0.8.11  # For DOCX file support
pydantic[email]==2.5.0

# Environment and configuration
python-dotenv>=1.0.0
pydantic>=2.0.0
pydantic-settings>=2.0.0
docx2txt>=0.8
PyMuPDF>=1.22.0
pdfplumber>=0.10.2

# Authentication and security
python-jose>=3.3.0
passlib>=1.7.4
bcrypt>=4.0.1

# AI and NLP
google-generativeai>=0.1.0
openai>=1.0.0
nltk>=3.8.1

# Data processing and visualization
pandas>=2.0.0
numpy>=1.24.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Testing and development
pytest>=7.0.0
pytest-asyncio>=0.21.0
httpx>=0.24.0  # For testing HTTP requests

# Utilities
python-dateutil>=2.8.2
requests>=2.31.0
aiohttp>=3.8.0  # For async HTTP requests
tqdm>=4.65.0  # For progress bars
chardet>=5.0.0
motor==3.3.2
