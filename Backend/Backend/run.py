from fastapi import <PERSON><PERSON><PERSON>, HTTPException, UploadFile, File, Form, Request, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from contextlib import asynccontextmanager
import os
import json
import time
import logging
import traceback
import uuid
import re
import hashlib
from enum import Enum
from datetime import datetime
import google.generativeai as genai
from dotenv import load_dotenv
import PyPDF2
import docx2txt
from tempfile import NamedTemporaryFile
import psycopg2
from psycopg2.extras import RealDictCursor
import pdfplumber
import docx
import io
import uvicorn

# Load environment variables
load_dotenv()

# Import local modules
from app.core.config import settings, logger, model, GOOGLE_API_KEY
from app.database.connection import get_db_connection, init_database
from app.services.services_candidate import (
    extract_text_from_file,
    generate_learning_path,
    validate_resume_content,
    validate_job_description,
    validate_resume_job_compatibility,
    clean_json_response,
    get_repeated_questions,
    get_enhanced_interview_stats,
    allowed_file
)
from app.models.schema import ExperienceLevel
from app.Candidate.schemas import BulkEvaluationRequest, BulkEvaluationResult, TranscriptSaveRequest
from app.Candidate.question_generator import generate_questions_from_resume_and_jd
from app.Candidate.answer_evaluator import evaluate_multiple_answers
from app.Candidate.behavior_analyzer import analyze_behavior

# Import database module
import app.database.connection as database

# Initialize MongoDB collections
try:
    from app.database.mongo_connection import get_mongo_db
    mongo_db = get_mongo_db()
    transcripts_collection = mongo_db.transcripts if mongo_db else None
except Exception as e:
    logger.warning(f"MongoDB connection failed: {str(e)}")
    transcripts_collection = None

# Global variable for storing latest questions
latest_questions = []
async def startup_tasks():
    """Tasks to run on application startup."""
    logger.info("Starting application...")
    db_initialized = database.init_database()
    if db_initialized:
        logger.info("✅ Database connection established and tables initialized")
        try:
            conn = database.get_db_connection()
            if conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM interview_questions")
                count = cursor.fetchone()
                cursor.close()
                conn.close()
                if count == 0:
                    logger.info("Database is empty. Consider seeding via /api/seed-database endpoint.")
                else:
                    logger.info(f"Database contains {count} interview questions")
        except Exception as e:
            logger.warning(f"Could not check database contents: {str(e)}")
    else:
        logger.warning("⚠️ Database connection failed - running without database features")
    
    logger.info("✅ Gemini API key configured" if GOOGLE_API_KEY else "❌ Gemini API key not configured")
    logger.info("🚀 Application startup completed successfully")

async def shutdown_tasks():
    """Tasks to run on application shutdown."""
    logger.info("🛑 Application shutting down...")

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    await startup_tasks()
    yield
    # Shutdown
    await shutdown_tasks()

# Initialize FastAPI app
app = FastAPI(title="Learning Path API", debug=True, lifespan=lifespan)

# CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add middleware to handle CORS and errors
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    try:
        start_time = time.time()
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        return response
    except Exception as e:
        logger.error(f"Unhandled exception in middleware: {str(e)}\n{traceback.format_exc()}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"Internal server error: {str(e)}"},
        )

# Create router for API endpoints
from fastapi import APIRouter
router = APIRouter()

# Include API routes
# app.include_router(router)  # We'll add routes directly to app for now

# Health Check Endpoint
@app.get("/api/health")
async def health_check():
    """Enhanced health check endpoint with system status."""
    try:
        # Check database connection
        db_status = "connected"
        try:
            conn = get_db_connection()
            if conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.close()
                conn.close()
            else:
                db_status = "disconnected"
        except Exception as e:
            db_status = f"error: {str(e)}"
        
        # Check Gemini API
        gemini_status = "configured" if GOOGLE_API_KEY else "not_configured"
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": "1.0.0",
            "services": {
                "database": db_status,
                "gemini_api": gemini_status
            },
            "uptime": "running"
        }
    
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }
# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Learning Path API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "upload": "/api/upload",
            "company_questions": "/api/company-questions",
            "interview_resources": "/api/interview-resources",
            "repeated_questions": "/api/repeated-questions",
            "interview_stats": "/api/interview-stats",
            "health": "/api/health",
            "docs": "/docs"
        },
        "timestamp": datetime.now().isoformat()
    }

# API Endpoints

@app.post("/generate-questions/")
async def generate_questions(resume: UploadFile = File(...), jd: UploadFile = File(...)):
    resume_content = await resume.read()
    jd_content = await jd.read()
    resume_text = extract_text(resume_content, resume.filename)
    jd_text = extract_text(jd_content, jd.filename)
    questions = await generate_questions_from_resume_and_jd(resume_text, jd_text)
    global latest_questions
    latest_questions = questions  # Store for later retrieval
    return {"questions": questions}

@app.get("/latest-questions/")
async def get_latest_questions():
    return {"questions": latest_questions}

def extract_text(file_bytes: bytes, filename: str) -> str:
    if filename.lower().endswith('.pdf'):
        with pdfplumber.open(io.BytesIO(file_bytes)) as pdf:
            return "\n".join(page.extract_text() or "" for page in pdf.pages)
    elif filename.lower().endswith('.docx'):
        doc = docx.Document(io.BytesIO(file_bytes))
        return "\n".join([para.text for para in doc.paragraphs])
    else:
        # fallback: try to decode as text
        return file_bytes.decode("utf-8", errors="ignore")

@app.post("/evaluate-answer/", response_model=BulkEvaluationResult)
async def evaluate_bulk_answers(request: BulkEvaluationRequest):
    evaluations = await evaluate_multiple_answers(request.answers)

    # Save to MongoDB after evaluation
    if transcripts_collection:
        transcript_entry = {
            "transcript": request.answers,
            "evaluations": [eval.model_dump() for eval in evaluations],
        }
        transcripts_collection.insert_one(transcript_entry)

    return BulkEvaluationResult(evaluations=evaluations)

@app.post("/analyze-behavior/")
async def analyze_behavior_metrics(video_url: str):
    try:
        result = await analyze_behavior(video_url)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/save-transcript")
async def save_transcript(data: TranscriptSaveRequest):
    try:
        if transcripts_collection:
            document = {
                "candidate_name": data.candidate_name,
                "questions": data.questions,
                "answers": data.answers,
                "transcript_text": data.transcript_text,
            }
            transcripts_collection.insert_one(document)
        return {"message": "Transcript saved successfully."}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    port = int(os.getenv("PORT", 8000))
    logger.info(f"Starting server on http://127.0.0.1:{port}")
    uvicorn.run("run:app", host="127.0.0.1", port=port, reload=True, log_level="info")
